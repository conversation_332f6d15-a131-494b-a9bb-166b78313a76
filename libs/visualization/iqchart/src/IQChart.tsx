'use client';
import React from 'react';

import { HoldingsQuote } from '@benzinga/quote-holdings-manager';
import { useAutoCompleteSymbolsCallback } from '@benzinga/pro-ui';
import { SymbolSearchItem, WatchlistSearchItem, LinkingSearchItem, ScannerSearchItem } from '@benzinga/search-modules';

import { CIQ } from 'chartiq';

import { IQChartInternal } from './internal';
import { Visualization, VisualizationArgs } from '@benzinga/visualization-utils';
import getLicenseKey from './key';
import { useQuoteHoldingsSubscriptionCallback } from '@benzinga/quotes-manager-hooks';
import { StockSymbol } from '@benzinga/session';

import { TbChartTreemap } from 'react-icons/tb';
import { arrayDifference, arrayShallowEqual, deepEqual, isNull, setDifference } from '@benzinga/utils';
import { useChartiqSwitchListener } from './useChartiqSwitchListener';
import { IQChartProps } from './entities';
import Hooks from '@benzinga/hooks';
import { EventChange } from './news markers/markerBuilder';
import { numItemsFromTemplate, SymbolComparison } from './comparasion';

getLicenseKey(CIQ);

/* eslint-disable no-irregular-whitespace */
// Sonyl Nagale11:11 AM
// https://license.chartiq.com/packager/download/3e6e80c4-e59b-4add-8e93-ca22f7e3b05a/benzinga-8.8.4-263c5425.zip
// Sonyl Nagale11:40 AM
// https://github.com/ChartIQ/chartiq-react-app/tree/master/react-components
// Sonyl Nagale12:24 PM
// <cq-chart-instructions role="contentinfo"></cq-chart-instructions>
// Sonyl Nagale12:44 PM
// https://documentation.chartiq.com/CIQ.ChartEngine2.html#updateChartData
// Thomas Cotter12:51 PM
// https://spx-dash.onrender.com/
// Sonyl Nagale1:26 PM
// https://gist.github.com/sonyl-ciq/2156a756dabf7c49a52e59f8b529ceb0
// Sonyl Nagale1:29 PM
// https://jsfiddle.net/user/chartiq
/* eslint-enable no-irregular-whitespace */

export type IQChartModuleItem = SymbolSearchItem | WatchlistSearchItem | LinkingSearchItem | ScannerSearchItem;

const updateChart = (nextSymbol: StockSymbol, currentSymbol: StockSymbol, chart: CIQ.ChartEngine) => {
  if (nextSymbol === undefined && (chart.chart.xAxis.noDraw === null || chart.chart.xAxis.noDraw === false)) {
    chart.loadChart(null as any, undefined, () => {
      chart.chart.xAxis.noDraw = true;
      chart.chart.yAxis.noDraw = true;
      (chart.controls as any).chartControls.style.visibility = 'hidden';
      const container = chart.container.closest('cq-context-wrapper')! as HTMLElement;
      // chart.layout.clearStudies();
      if (!container.querySelector('.ciq-empty-message')) {
        chart.container.insertAdjacentHTML('beforeend', `<div class='ciq-empty-message'>No Chart</div>`);
        container.classList.add('ciq-empty-chart');
      }
      chart.draw();
    });
  } else if (nextSymbol === currentSymbol) {
    return;
  } else if (nextSymbol !== currentSymbol) {
    chart.loadChart(nextSymbol, undefined, () => {
      chart.chart.xAxis.noDraw = false;
      chart.chart.yAxis.noDraw = false;
      (chart.controls as any).chartControls.style.visibility = 'visible';

      const container = chart.container.closest('cq-context-wrapper')! as HTMLElement;
      const emptyChartOverlay = container.querySelector('.ciq-empty-message');
      emptyChartOverlay?.remove();
      container.classList.remove('ciq-empty-chart');
      chart.draw();
      chart.home({ animate: false });
    });
  } else {
    return;
  }
};

const getSymbolToChartMapping = (charts: CIQ.ChartEngine[]) =>
  charts.reduce((acc, chart) => {
    const symbol = chart.getSymbols({})[0]?.symbol;
    if (acc.has(symbol)) {
      acc.set(symbol, [...acc.get(symbol)!, chart]);
    } else {
      acc.set(symbol, [chart]);
    }
    return acc;
  }, new Map<StockSymbol, CIQ.ChartEngine[]>());

const IQChart: React.FC<VisualizationArgs<IQChartProps>> = props => {
  // const [quotes, setQuotes] = useState(useAutoCompleteQuoteSubscription(props.sources) ?? []);
  const prevVolume = React.useRef<number | undefined>(undefined);
  const [stxx, setStxx] = React.useState<CIQ.UI.CIQElement | null>(null);
  const firstLoad = React.useRef(true);
  const [page, setPage] = React.useState(0);

  const [symbols, setSymbols] = React.useState<StockSymbol[]>([]);
  const symbolToEngine = React.useRef(new Map<StockSymbol, CIQ.ChartEngine[]>());

  const trace = props.args.traces[0];
  const syncTicker = trace.gridSettings?.syncTicker ?? false;

  const updateSymbolsInChart = React.useCallback(
    (symbols: StockSymbol[]) => {
      setStxx(stxx => {
        if (stxx === null) {
          return stxx;
        }

        const items =
          stxx?.colCount && stxx?.rowCount
            ? stxx.colCount * stxx.rowCount
            : stxx?.gridTemplate
              ? numItemsFromTemplate(stxx?.gridTemplate)
              : 1;

        const charts = stxx.getCharts();
        charts.pop(); // remove main chart

        let upToDate = true;

        if (syncTicker) {
          const nextSymbol = symbols[0];

          const currentSymbols = charts.flatMap<StockSymbol>(chart => {
            const symbol = chart.getSymbols({})[0]?.symbol;
            return symbol ? [symbol] : [];
          });

          upToDate = currentSymbols.length === charts.length && currentSymbols.every(c => c === nextSymbol);
          if (!upToDate) {
            charts.forEach((chart, index) => updateChart(nextSymbol, currentSymbols[index], chart));
          }
        } else {
          const nextSymbols = symbols.slice(page * items, (page + 1) * items);

          const currentSymbols = charts.flatMap<StockSymbol>(chart => {
            const symbol = chart.getSymbols({})[0]?.symbol;
            return symbol ? [symbol] : [];
          });

          upToDate = arrayShallowEqual(nextSymbols, currentSymbols);
          if (!upToDate) {
            charts.forEach((chart, index) => updateChart(nextSymbols[index], currentSymbols[index], chart));
          }
        }

        if (!upToDate) {
          symbolToEngine.current = getSymbolToChartMapping(charts);
        }
        return stxx;
      });
    },
    [page, syncTicker],
  );

  const stxxRef = React.useRef<CIQ.UI.CIQElement | null>(null);
  stxxRef.current = stxx;

  const prvComparisons = React.useRef<Record<string | number, string[]>>({});

  const updatePrvComparison = React.useCallback((args: Record<string | number, string[]>) => {
    prvComparisons.current = args;
  }, []);

  const currently_updating = React.useRef(0);
  const updateSync = React.useCallback(
    (e: CIQ.ChartEngine & { stx: CIQ.ChartEngine }, chartIndex: number) => {
      const onChange = props.onChange;
      currently_updating.current += 1;
      if (currently_updating.current === 1) {
        onChange(args => {
          if (args.traces[0]?.gridSettings?.syncInterval) {
            const newPeriodicity = e.stx.getPeriodicity();
            stxxRef.current
              ?.getCharts()
              ?.forEach(c => !deepEqual(c.getPeriodicity(), newPeriodicity) && c.setPeriodicity(newPeriodicity as any));
          }
          if (args.traces[0]?.gridSettings?.syncStudies) {
            stxxRef.current?.getCharts()?.forEach(c => {
              if (
                c !== e.stx &&
                !deepEqual(
                  Object.values(c.layout.studies).map(s => s.name),
                  Object.values(e.stx.layout.studies).map(s => s.name),
                )
              ) {
                Object.values(c.layout.studies).forEach(s => CIQ.Studies.removeStudy(c, s));

                Object.values(e.stx.layout.studies).forEach(s =>
                  CIQ.Studies.addStudy(c, s.type, s.inputs, s.outputs, s.parameters, s.panel),
                );
              }
            });
          }
          const symbols = e.stx.layout.symbols.map(a => a.symbol);
          if (args.traces[0]?.gridSettings?.syncComparisons) {
            const removedItems = arrayDifference(prvComparisons.current['default'] ?? [], symbols);
            const addedItems = arrayDifference(symbols, prvComparisons.current['default'] ?? []);

            const removeIndexes = removedItems.map(r => prvComparisons.current['default']?.findIndex(s => s === r));

            const colors = addedItems.map(a => e.stx.layout.symbols.find(s => s.symbol === a)?.parameters.color);
            stxxRef.current?.getCharts()?.forEach(c => {
              removedItems.forEach(symbol => c.removeSeries(symbol));
              addedItems.forEach((symbol, index) =>
                c.addSeries(symbol, {
                  color: colors[index],
                  isComparison: true,
                }),
              );
            });

            args = {
              ...args,
              traces: args.traces.map(trace => ({
                ...trace,
                comparison: {
                  default: trace.comparison.default?.filter((_, index) => !removeIndexes.includes(index)),
                },
              })),
            };
          } else {
            const removedItems = arrayDifference(prvComparisons.current[chartIndex] ?? [], symbols);
            const removeIndexes = removedItems.map(r => prvComparisons.current[chartIndex]?.findIndex(s => s === r));
            args = {
              ...args,
              traces: args.traces.map(trace => ({
                ...trace,
                comparison: {
                  [chartIndex]: trace.comparison[chartIndex]?.filter((_, index) => !removeIndexes.includes(index)),
                },
              })),
            };
          }

          args = {
            ...args,
            traces: args.traces.map(trace => ({
              ...trace,
              chartsConfig: stxxRef.current?.getCharts().map(c => c.exportLayout(true)) ?? [],
              gridLayout: {
                colCount: stxxRef.current?.colCount ?? 1,
                gridTemplate: stxxRef.current?.gridTemplate ?? undefined,
                offgridCharts: stxxRef.current?.offgridCharts ?? undefined,
                rowCount: stxxRef.current?.rowCount ?? 1,
              },
            })),
          };

          return args;
        });
      }
      currently_updating.current -= 1;
    },
    [props.onChange],
  );

  const [charts, setCharts] = React.useState(stxx?.getCharts());

  React.useEffect(() => {
    const selfCharts = charts;
    const subscriptions = selfCharts?.map((c, index) => {
      const cb = (e: CIQ.ChartEngine & { stx: CIQ.ChartEngine }) => updateSync(e, index);
      return {
        cb,
        subscription: c.addEventListener?.('layout', cb),
      };
    });
    return () =>
      selfCharts?.forEach(
        (c, i) =>
          subscriptions?.[i].subscription &&
          c.removeEventListener(subscriptions[i].subscription as any, subscriptions[i].cb),
      );
  }, [charts, props.onChange, stxx, updateSync]);

  const currently_event_updating = React.useRef(0);
  const onEventChange = React.useCallback(
    (event: EventChange) => {
      currently_event_updating.current += 1;
      if (currently_event_updating.current === 1) {
        const onChange = props.onChange;
        onChange(args => {
          if (args.traces[0]?.gridSettings?.syncEvents) {
            const activeLabels = event.activeLabels;

            const charts = stxxRef.current?.getCharts();
            charts?.forEach(chart => {
              const implementation = chart.uiContext.advertised.Markers.implementation;
              const labels = implementation.getActiveLabels();
              const addedLabels = setDifference(activeLabels, labels);
              const removedLabels = setDifference(labels, activeLabels);
              [...addedLabels].forEach(l => implementation.showMarkers(l));
              [...removedLabels].forEach(l => implementation.hideMarkers(l));
            });
          }
          return args;
        });
      }
      currently_event_updating.current -= 1;
    },
    [props.onChange],
  );

  const firstRender = Hooks.useIsFirstRenderRef();

  const symbolsRef = React.useRef<StockSymbol[] | undefined>(undefined);
  symbolsRef.current = symbols;
  React.useEffect(() => {
    if (!firstRender.current) {
      updateSymbolsInChart(symbolsRef.current ?? []);
    }
  }, [firstRender, stxx, updateSymbolsInChart]);

  React.useEffect(() => {
    const cb = () => {
      updateSymbolsInChart(symbolsRef.current ?? []);
      const charts = stxxRef.current?.getCharts();
      setCharts(charts);
      const chart: any = charts?.[0];
      if (chart) {
        chart.stx = chart;
        updateSync(chart, 0);
      }
    };
    stxx?.addEventListener?.('update-grid', cb);
    return () => stxx?.removeEventListener('update-grid', cb);
  }, [stxx, updateSymbolsInChart, updateSync]);

  const syncInterval = trace.gridSettings?.syncInterval ?? false;
  const syncStudies = trace.gridSettings?.syncStudies ?? false;
  const syncEvents = trace.gridSettings?.syncEvents ?? false;
  const syncComparisons = trace.gridSettings?.syncComparisons ?? false;

  useChartiqSwitchListener(stxx, syncTicker, 'syncTicker', '.bz-grid-sync-ticker', props.onChange);
  useChartiqSwitchListener(stxx, syncInterval, 'syncInterval', '.bz-grid-sync-interval', props.onChange);
  useChartiqSwitchListener(stxx, syncStudies, 'syncStudies', '.bz-grid-sync-studies', props.onChange);
  useChartiqSwitchListener(stxx, syncEvents, 'syncEvents', '.bz-grid-sync-events', props.onChange);
  useChartiqSwitchListener(stxx, syncComparisons, 'syncComparisons', '.bz-grid-sync-comparisons', props.onChange);

  useAutoCompleteSymbolsCallback(
    props.sources,
    React.useCallback(
      symbols => {
        const sArray = Array.from(symbols);
        setSymbols(sArray);

        updateSymbolsInChart(sArray);
      },
      [updateSymbolsInChart],
    ),
  );

  const quotesRef = React.useRef<HoldingsQuote[] | undefined>(undefined);

  const updateQuotesInChart = React.useCallback((quotes: HoldingsQuote[]) => {
    quotes.forEach(quote => {
      const engine = symbolToEngine.current.get(quote.symbol);
      engine?.forEach(e =>
        e?.updateChartData(
          {
            Ask: quote.askPrice ?? 0,
            Bid: quote.bidPrice ?? 0,
            DT: new Date(quote.lastTradeTime ?? '') as any,
            Last: quote.currentPrice ?? 0,
            Volume: prevVolume.current && quote.volume ? quote.volume - prevVolume.current : undefined,
          },
          null as any,
          {
            bypassGovernor: true,
            fillGaps: true,
          },
        ),
      );
    });
  }, []);

  useQuoteHoldingsSubscriptionCallback(
    symbols,
    React.useCallback(
      quotes => {
        quotesRef.current = quotes;
        if (isNull(stxx)) {
          return;
        }
        updateQuotesInChart(quotes);
      },
      [stxx, updateQuotesInChart],
    ),
  );

  const onEngineUpdate = React.useCallback(
    (stxx: CIQ.UI.CIQElement | null) => {
      setStxx(stxx);
      if (isNull(stxx)) {
        return;
      }

      setCharts(stxx.getCharts());

      // stxx.on;
      updateSymbolsInChart(symbolsRef.current ?? []);
      if (stxx) {
        quotesRef.current && updateQuotesInChart(quotesRef.current);
      }
    },
    [updateQuotesInChart, updateSymbolsInChart],
  );

  React.useEffect(() => {
    return () => {
      firstLoad.current = true;
    };
  }, []);

  return (
    <>
      <IQChartInternal onChange={props.onChange} onEngineUpdate={onEngineUpdate} onEvent={onEventChange} />
      <SymbolComparison
        onChange={props.onChange}
        onComparisonSymbolsChange={updatePrvComparison}
        stxx={stxx}
        trace={trace}
      />
    </>
  );
};

interface AddSymbol {
  symbol: string;
  index: number;
}

interface RemoveSymbol {
  symbol: string;
  previousIndex: number;
}

interface MoveSymbol {
  index: number;
  previousIndex: number;
  symbol: string;
}

export const delta = (prev: StockSymbol[], next: StockSymbol[]) => {
  const adds: AddSymbol[] = [];
  const removes: RemoveSymbol[] = [];
  const moves: MoveSymbol[] = [];

  const prevIndexs = prev.reduce((acc, symbol, index) => {
    acc.set(symbol, index);
    return acc;
  }, new Map<StockSymbol, number>());

  const nextIndex = next.reduce((acc, symbol, index) => {
    acc.set(symbol, index);
    return acc;
  }, new Map<StockSymbol, number>());

  prev.forEach((symbol, index) => {
    if (!nextIndex.has(symbol)) {
      removes.push({ previousIndex: index, symbol: symbol });
    }
  });

  next.forEach((symbol, index) => {
    const previousIndex = prevIndexs.get(symbol);
    if (previousIndex === undefined) {
      adds.push({ index, symbol });
    } else if (index !== previousIndex) {
      moves.push({ index, previousIndex, symbol });
    }
  });
  return {
    adds,
    moves,
    removes,
  };
};

export const BzChart: Visualization<IQChartProps> = {
  defaultArgs: {
    layout: {},
    traces: [
      {
        chartsConfig: [],
        comparison: { default: [] },
        gridLayout: {
          colCount: 1,
          gridTemplate: undefined,
          offgridCharts: undefined,
          rowCount: 1,
        },
        name: 'bz-chart',
        type: 'bz-chart',
      },
    ],
  },
  editorConfig: {
    traceDeclaration: {
      args: [
        {
          args: [],
          enum: 'bz-chart',
          icon: TbChartTreemap,
          label: 'Heatmap 2D',
          set: (args, path) => ({
            ...args,
            traces: args.traces.flatMap((trace, index) =>
              index === path
                ? [
                    {
                      chartsConfig: [],
                      comparison: { default: [] },
                      gridLayout: {
                        colCount: 1,
                        gridTemplate: undefined,
                        offgridCharts: undefined,
                        rowCount: 1,
                      },
                      name: 'bz-chart',
                      type: 'bz-chart',
                    },
                  ]
                : [trace],
            ),
          }),
        },
      ],
    },
  },
  type: 'bz-chart',
  typeDisplayName: 'BZ Line Chart',
  visualization: IQChart,
};
