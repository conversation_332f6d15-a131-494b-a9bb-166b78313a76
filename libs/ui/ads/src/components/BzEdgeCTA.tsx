'use client';
import { useEffect, useState } from 'react';
import styled from '@benzinga/themetron';

interface Props {
  onlyGraphic?: boolean;
  type?: string;
}

const typeMap = {
  'analyst-predictions': {
    buttonText: 'Click to Join',
    description:
      'With a Benzinga Edge membership, you can sort by most accurate analysts, upside targets, tickers, and more.',
    subTitle: 'All the Analyst Info You Need in One Place',
    textCTA: `With a Benzinga Edge membership, you can sort by most accurate analysts, upside targets, tickers, and more. Click her for access.`,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=analyst-ratings-text-ads',
    title: 'Analyst Ratings',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=analyst-ratings-graphic-ads',
  },
  'analyst-ratings': {
    buttonText: 'Click to Join',
    description:
      "Join <PERSON> and unlock all the major upgrades, downgrades, and changes to the market's most accurate analysts.",
    subTitle: '',
    textCTA: `CLICK HERE to join Benzinga Edge and unlock all the major upgrades, downgrades, and changes to the market's most accurate analysts.`,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=analyst-calendar-text-ads',
    title: 'Analyst Calendar',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=analyst-calendar-graphic-ads',
  },
  'basic-calendar': {
    buttonText: 'Click to Join',
    description: 'Unlock all calendars and get access to the best trading ideas, research, and analysis.',
    subTitle: 'CLICK HERE to join Benzinga Edge',
    textCTA: `Unlock all calendars and get access to the best trading ideas, research, and analysis.`,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=calendar-widget-text-ads',
    title: 'Calendars',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=calendar-widget-graphic-ads',
  },
  'bulls-vs-bears': {
    buttonText: 'Click Here to Unlock',
    description: "Click to unlock major analysts' bullish and bearish positions by joining Benzinga Edge.",
    subTitle: '',
    textCTA: `Click to unlock major analysts' bullish and bearish positions by joining Benzinga Edge.`,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=bulls-vs-bears-texst-ads',
    title: 'Bulls vs Bears',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=bulls-vs-bears-graphic-ads',
  },
  earnings: {
    buttonText: 'Click to Join',
    description:
      'Sort by estimates, projected upside, profit surprises, and more to easily find new stocks to invest in or check up on your portfolio.',
    subTitle: 'Never Miss Important Catalysts',
    textCTA: `Never miss the most important catalysts on your portfolio. Benzinga Edge members get full access to the earnings calendar plus other high-impact tools and content. CLICK HERE for more info.`,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=earnings-calendar-text-ads',
    title: 'Earnings Calendar',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=earnings-calendar-graphic-ads',
  },
  'gov-trades': {
    buttonText: 'Get Access',
    description: 'Get access to all of the buys and sells made by government employees, the ultimate insiders.',
    subTitle: 'Will Nancy Pelosi go down in history as the best trader to ever play the game?',
    textCTA: `
      Will Nancy Pelosi go down in history as the best trader to ever play the game?
      CLICK HERE to get access to all of the buys and sells made by government employees, the ultimate insiders.
    `,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=government-trades-text-ads',
    title: 'Government Trades',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=government-trades-graphic-ads',
  },
  'insider-report': {
    buttonText: 'Click to Join',
    description: 'Always know which  catalysts might be driving markets each week and how to trade them.',
    subTitle: 'Get 3 Trades a Week Plus Top Market-Moving Catalysts',
    textCTA: `Get a headstart on the markets every Sunday with expert analysis and three stocks to buy! The 'Insider Report' gives you exactly which market catalysts and major news events might be driving markets each week. Get immediate access with a Benzinga Edge membership! Click here for more info. `,
    textURL: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=insider-report-text-ads',
    title: 'Insider Report',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=insider-report-graphic-ads',
  },
  'insider-trades': {
    buttonText: 'Click to Join',
    description:
      "There's no better early warning system or confidence indicator than knowing the insider transactions on your stocks.",
    subTitle: 'Your Ultimate Sentiment Indicator',
    textCTA: `CLICK HERE to get access to all of the insider's buys and sells with Benzinga Edge. There's no better early warning system or confidence indicator than knowing the insider transactions on your stocks.`,
    textURL: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=insider-trades-text-ads',
    title: 'Insider Trades',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=insider-trades-graphic-ads',
  },
  'stock-of-the-day': {
    buttonText: 'Click to Join',
    description:
      'Every market day, Benzinga Edge readers get a stock pick setting up for an interesting technical move.',
    subTitle: 'Trading Setups Delivered Daily',
    textCTA: `Get stock tips with actionable setups with Benzinga Edge.
      Edge readers see stocks making new breakouts, breakdowns, and other tradeable setups with our 'Stock of the Day'. Click here for access. `,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=stock-of-the-day-text-ads',
    title: 'Stock of the Day',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=stock-of-the-day-graphic-ads',
  },
  'stock-report': {
    buttonText: 'Click to Join',
    description:
      'Get valuable price predictions, sentiment analysis, and financial health scores to make better-informed investing decisions.',
    subTitle: 'Unlock unlimited stock reports',
    textCTA: `Unlock unlimited stock reports on ticker pages with Benzinga Edge. CLICK HERE to get valuable price predictions, sentiment analysis, and financial health scores to make better-informed investing decisions.`,
    textURL: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=stock-analysis-text-ads',
    title: 'Stock Analysis',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=stock-analysis-graphic-ads',
  },
  'unusual-options-activity': {
    buttonText: 'Click to Join',
    description:
      'See what positions smart money is taking on your favorite stocks with the Benzinga Edge Unusual Options board.',
    subTitle: 'Identify Smart Money Moves',
    textCTA: `The Benzinga Edge Unusual Options board provides you with actionable insights on what's happening in markets. Join now to get the inside scoop on what positions smart money is taking on your favorite stocks. Click here for more info.`,
    textURL:
      'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=unusual-options-activity-text-ads',
    title: 'Options Activity',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=unusual-options-activity-graphic-ads',
  },
  'whisper-index': {
    buttonText: 'Click to Join',
    description: 'The Weekly Whisper Index reveals five under-the-radar stocks gaining investor attention.',
    subTitle: 'Exclusive for Benzinga Edge members!',
    textCTA: `
      Exclusive for Benzinga Edge members! The Weekly Whisper Index reveals five under-the-radar stocks gaining investor attention.
      CLICK HERE for access.
    `,
    textURL: `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=whisper-index-text-ads`,
    title: 'Whisper Index',
    url: 'https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?adType=paywall&ad=whisper-index-graphic-ads',
  },
};

const edgeLogo = (
  <svg fill="none" viewBox="0 0 96 9" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.3078 8.9578H16.7882V7.28699H12.272V5.23708H16.014V3.56627H12.272V1.67081H16.8169V0H10.3078V8.9578ZM24.5877 5.64426L20.3438 0H17.4191L18.3366 1.13728V8.98588H20.2865V3.10294L24.6737 8.9578H26.5518V0H24.5877V5.64426ZM7.35437 4.35254C8.2146 4.19809 8.93146 3.41183 8.93146 2.30263C8.93146 1.10919 8.04255 0 6.27908 0H0.601562L1.49047 1.10919V8.97184H6.4798C8.24328 8.97184 9.16085 7.89073 9.16085 6.54285C9.11784 5.43365 8.34364 4.4789 7.35437 4.35254ZM3.44032 1.68485H5.89198C6.55149 1.68485 6.96727 2.06395 6.96727 2.62556C6.96727 3.18718 6.55149 3.56627 5.89198 3.56627H3.44032V1.68485ZM5.978 7.30103H3.44032V5.23708H5.978C6.72354 5.23708 7.13931 5.70042 7.13931 6.26204C7.13931 6.9079 6.69486 7.28699 5.978 7.30103ZM55.1975 5.64426H56.2155V6.69729C55.685 7.1185 55.0255 7.35719 54.3373 7.37123C52.7028 7.37123 51.5559 6.14971 51.5559 4.50698C51.5559 2.86425 52.7172 1.64273 54.3373 1.64273C55.1832 1.64273 55.9717 2.07799 56.4305 2.78001L57.9789 1.93758C57.3194 0.912628 56.1581 0 54.323 0C51.7279 0 49.5774 1.75505 49.5774 4.4789C49.5774 7.20275 51.6992 8.9578 54.323 8.9578C55.7567 8.98588 57.1187 8.38214 58.0649 7.34315V4.02961H53.8785L55.1975 5.64426ZM46.3801 5.64426L42.122 0H39.2116L40.1148 1.13728V8.98588H42.079V3.10294L46.4662 8.9578H48.33V0H46.3801V5.64426ZM64.5453 0H60.7747L61.6779 1.13728L61.5202 1.57253L58.5954 8.98588H60.8033L61.3768 7.46952H65.3196L65.8931 8.98588H68.101L64.5453 0ZM61.8786 5.78466L63.3124 1.92354L64.7461 5.78466H61.8786ZM36.1147 0H38.0789V8.99992H36.1147V0ZM34.552 1.54445V0H27.6558V1.67081H31.9856L27.6558 7.42739V8.9578H34.6237V7.28699H30.1935L34.552 1.54445Z"
      fill="white"
    />
    <path d="M67.2051 0H96.0045V8.99992H70.8051L67.2051 0Z" fill="#3F83F8" />
    <path
      d="M92.5942 7.11658H88.8926L90.0454 1.88574H93.747L93.5274 2.84251H90.9473L90.7042 3.97964H93.2294L93.0176 4.93641H90.4846L90.2179 6.15197H92.7981L92.5942 7.11658Z"
      fill="white"
    />
    <path
      d="M83.498 4.86616C83.498 3.10163 84.8312 1.7998 86.6428 1.7998C87.7564 1.7998 88.4779 2.40366 88.7995 3.07026L87.7721 3.50943C87.5839 3.0781 87.0663 2.78794 86.486 2.78794C85.5135 2.78794 84.6587 3.65059 84.6587 4.79557C84.6587 5.58765 85.2155 6.22288 86.1331 6.22288C86.5801 6.22288 86.9251 6.05035 87.1918 5.82292L87.3251 5.18769H86.7702L86.1488 4.23093H88.6505L88.1956 6.25425C87.6859 6.85811 86.933 7.21101 86.0703 7.21101C84.643 7.21101 83.498 6.30914 83.498 4.86616Z"
      fill="white"
    />
    <path
      d="M78.772 1.88574H80.7953C81.9559 1.88574 83.0617 2.77977 83.0617 4.13649C83.0617 5.5089 82.1285 7.11658 79.6738 7.11658H77.6191L78.772 1.88574ZM78.9523 6.13629H79.8777C81.1403 6.13629 81.9011 5.2501 81.9011 4.20707C81.9011 3.44637 81.3599 2.86603 80.5835 2.86603H79.6738L78.9523 6.13629Z"
      fill="white"
    />
    <path
      d="M76.7485 7.11658H73.0469L74.1997 1.88574H77.9013L77.6817 2.84251H75.1016L74.8585 3.97964H77.3837L77.1719 4.93641H74.6389L74.3722 6.15197H76.9524L76.7485 7.11658Z"
      fill="white"
    />
  </svg>
);

export const BzEdgeCTA: React.FC<Props> = ({ onlyGraphic = false, type = '' }) => {
  const variant = Object.keys(typeMap).includes(type) ? typeMap[type] : typeMap['stock-report'];
  const { buttonText, description, subTitle, textCTA, textURL, title, url } = variant;
  const [showTextVersion, setShowTextVersion] = useState(false);

  useEffect(() => {
    if (!onlyGraphic) {
      const versionIndex = Math.floor(Math.random() * 2);
      setShowTextVersion(versionIndex === 1);
    }
  }, [onlyGraphic]);

  const handleClick = link => {
    window.open(link, '_blank');
  };

  if (showTextVersion) {
    return (
      <TextAd className={type} onClick={() => handleClick(textURL)}>
        <p>{textCTA}</p>
      </TextAd>
    );
  }

  return (
    <Wrapper className={type} onClick={() => handleClick(url)}>
      <div className="banner">
        <div className="content">
          <h2>{title}</h2>
          {subTitle && <h3>{subTitle}</h3>}
          <p>{description}</p>
        </div>
        <div className="button-wrapper">
          <div className="logo">{edgeLogo}</div>
          <button>{buttonText}</button>
        </div>
      </div>
      <div className="banner-bg-end"></div>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  height: 200px;
  width: 100%;
  //max-width: 600px;
  margin: 0 auto;
  background: linear-gradient(180deg, #0c1c33 0%, #050e18 100%);
  position: relative;
  cursor: pointer;

  .banner {
    background-image: url('/next-assets/images/banners/edge/cta/cta-bg.png');
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
  }

  .button-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    gap: 1rem;
    z-index: 10;
  }

  .logo {
    width: 200px;
    height: 40px;
  }

  .content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    max-width: 500px;
    z-index: 10;
  }

  h2 {
    color: #f2f8ff;
    font-size: 24px;
    font-weight: 700;
    text-transform: uppercase;
  }
  h3 {
    color: #e1ebfa;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
  }
  p {
    color: #ceddf2;
    font-size: 16px;
    line-height: 24px;
  }

  button {
    background: #3f83f8;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 18px;
  }

  @media (max-width: 800px) {
    .logo {
      width: 80px;
      height: 20px;
    }

    .content {
      padding: 0;
    }

    .banner {
      height: 100px;
    }
    .banner-bg-end {
      background-size: cover;
    }

    h2 {
      font-size: 16px;
      line-height: 1;
    }
    h3 {
      font-size: 12px;
      line-height: 1;
    }
    p {
      font-size: 12px;
      line-height: 14px;
    }

    button {
      font-size: 10px;
      padding: 4px 8px;
    }
  }

  @media (max-width: 600px) {
    .banner {
      padding: 0.75rem;
    }
  }

  /* Variations */
  &.gov-trades {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-gov-trades.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      h2 {
        font-size: 14px;
      }
      h3 {
        font-size: 10px;
      }
      p {
        font-size: 10px;
      }
      .banner-bg-end {
        height: 100px;
        overflow: hidden;
      }
    }
  }

  &.stock-of-the-day {
    max-width: 764px;
    height: 180px;
    h3 {
      font-size: 20px;
      font-weight: 700;
    }
    .banner {
      height: 180px;
    }
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-stock-of-the-day.png');
      width: 100%;
      height: 180px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }

    @media (max-width: 1000px) {
      .logo {
        width: 160px;
        height: 20px;
      }
      h2 {
        font-size: 20px;
      }
      h3 {
        font-size: 16px;
      }
      p {
        font-size: 14px;
        line-height: 14px;
      }
      button {
        font-size: 16px;
      }
    }

    @media (max-width: 800px) {
      height: 100px;
      h2 {
        font-size: 16px;
      }
      h3 {
        font-size: 12px;
      }
      p {
        font-size: 10px;
      }
      .banner,
      .banner-bg-end {
        height: 100px;
        overflow: hidden;
      }

      .logo {
        width: 80px;
        height: 20px;
      }
      button {
        font-size: 10px;
        padding: 4px 8px;
      }
    }
  }

  &.whisper-index {
    max-width: 764px;
    height: 180px;
    h3 {
      font-size: 20px;
      font-weight: 700;
    }
    .banner {
      height: 180px;
    }
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-whisper-index.png');
      width: 100%;
      height: 180px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }

    @media (max-width: 1000px) {
      .logo {
        width: 160px;
        height: 20px;
      }
      h2 {
        font-size: 20px;
      }
      h3 {
        font-size: 16px;
      }
      p {
        font-size: 14px;
        line-height: 14px;
      }
      button {
        font-size: 16px;
      }
    }

    @media (max-width: 800px) {
      height: 100px;
      h2 {
        font-size: 16px;
      }
      h3 {
        font-size: 12px;
      }
      p {
        font-size: 10px;
      }
      .banner,
      .banner-bg-end {
        height: 100px;
        overflow: hidden;
      }
      .logo {
        width: 80px;
        height: 20px;
      }
      button {
        font-size: 10px;
        padding: 4px 8px;
      }
    }
  }

  &.unusual-options-activity {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-unusual-options.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      .banner,
      .banner-bg-end {
        height: 100px;
      }
    }
  }

  &.insider-trades {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-insider-trades.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      .banner,
      .banner-bg-end {
        height: 100px;
      }
    }
  }

  &.earnings {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-earnings.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      .banner,
      .banner-bg-end {
        height: 100px;
      }
      p {
        font-size: 10px;
      }
    }
  }

  &.analyst-ratings {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-analyst-calendar.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      .banner,
      .banner-bg-end {
        height: 100px;
      }
    }
  }

  &.analyst-predictions {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-analyst.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      .banner,
      .banner-bg-end {
        height: 100px;
      }
    }
  }

  &.bulls-vs-bears {
    height: 80px;
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-bulls-vs-bears.png');
      width: 100%;
      height: 80px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    .logo {
      width: 80px;
      height: 20px;
    }
    .banner {
      height: 80px;
    }

    h2 {
      font-size: 16px;
    }

    p {
      font-size: 10px;
      line-height: 12px;
      padding-right: 0.5rem;
    }

    button {
      font-size: 10px;
      padding: 4px 8px;
      white-space: nowrap;
    }
  }

  &.stock-report {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-stock-report.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      .banner,
      .banner-bg-end {
        height: 100px;
      }
      p {
        font-size: 10px;
      }
    }
  }

  &.basic-calendar {
    .banner-bg-end {
      background-image: url('/next-assets/images/banners/edge/cta/bg-earnings.png');
      width: 100%;
      height: 200px;
      background-size: contain;
      background-position: right;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      z-index: 1;
    }
    @media (max-width: 800px) {
      height: 100px;
      .banner,
      .banner-bg-end {
        height: 100px;
      }
      p {
        font-size: 10px;
      }
    }
  }
`;

const TextAd = styled.div`
  background: #0c1c33;
  color: #ceddf2;
  padding: 1rem;
  margin: 0 auto;
  max-width: 600px;
  text-align: center;
  cursor: pointer;

  p {
    font-size: 16px;
    line-height: 24px;
  }

  &.stock-of-the-day,
  &.whisper-index {
    max-width: 764px;
  }

  @media (max-width: 800px) {
    p {
      font-size: 12px;
      line-height: 16px;
    }
  }
`;
