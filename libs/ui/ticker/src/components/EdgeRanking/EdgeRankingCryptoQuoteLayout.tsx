import React, { useState, useEffect } from 'react';
import styled from '@benzinga/themetron';
import { usePermission } from '@benzinga/user-context';
import { Button } from '@benzinga/core-ui';
import { RankingDetail } from '@benzinga/quotes-manager';
import { usePaywallText } from './utils';
import { RankingItem } from './RankingItem';
import { GetPriceTrend } from './GetPriceTrend';

interface EdgeRankingCryptoQuoteLayoutProps extends RankingDetail {
  className?: string;
  showRanking?: boolean;
  symbol: string;
  adType: string;
}

export const EdgeRankingCryptoQuoteLayout: React.FC<EdgeRankingCryptoQuoteLayoutProps> = ({
  adType,
  className = '',
  longTermTrend = 'Y',
  mediumTermTrend = 'Y',
  momentum,
  shortTermTrend = 'Y',
}) => {
  const [show, setShow] = useState(false);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');

  useEffect(() => {
    if (hasPermission) {
      setShow(true);
    }
  }, [hasPermission]);

  if (!show) {
    return <GetAccessToEdgeRanking adType={adType} />;
  }

  return (
    <EdgeRankingCryptoQuoteLayoutWrapper className={className}>
      <div className="ranking-wrap flex flex-col md:flex-row w-full">
        <div className="rankings-data flex flex-col bg-wrap md:flex-row w-full md:w-auto">
          <div className="ranking-row ">{RankingItem({ label: 'Momentum', value: momentum ?? 0 })}</div>
          <div>
            <div className="position-type flex flex-col">
              <h5 className="leading-none mb-1">Price Trend</h5>
              <div className="flex gap-2 flex flex-col sm:flex-row">
                {GetPriceTrend(shortTermTrend ?? '', 'Short')}
                {GetPriceTrend(mediumTermTrend ?? '', 'Medium')}
                {GetPriceTrend(longTermTrend ?? '', 'Long')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </EdgeRankingCryptoQuoteLayoutWrapper>
  );
};

export default EdgeRankingCryptoQuoteLayout;

const GetAccessToEdgeRanking = ({ adType }: { adType: string }) => {
  const [paywallLabel, paywallDesc, paywallButtonLabel, buyNowLink] = usePaywallText(adType);

  return (
    <GetEdgeRankingWrap className="get-access-wrap">
      <div className="flex flex-col sm:flex-row  justify-between items-center w-full gap-1">
        <div className="access-header">
          <h3>{paywallLabel}</h3>
          <p>{paywallDesc}</p>
        </div>
        <Button as="a" href={buyNowLink} variant="flat-blue">
          {paywallButtonLabel}
        </Button>
      </div>
    </GetEdgeRankingWrap>
  );
};

const GetEdgeRankingWrap = styled.div`
  background-color: #f2f8ff;
  border: 1px solid #ceddf2;
  border-radius: 4px;
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  a {
    min-width: 140px;
    padding: 12px 10px;
    @media (max-width: 768px) {
      min-width: 140px;
      padding: 12px 10px;
    }
  }
  .access-header {
    h3 {
      font-size: 1.2rem;
      color: #192940;
    }
    p {
      color: #5b7292;
      font-size: 14px;
    }
  }
  button {
    font-size: 16px;

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }
`;

const EdgeRankingCryptoQuoteLayoutWrapper = styled.div`
  .ranking-wrap {
    flex-grow: 1;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    width: 100%;
    .bg-wrap {
      background-color: #f2f8ff;
      border: 1px solid #ceddf2;
      border-radius: 4px;
      padding: 1rem;
    }
    h5 {
      font-size: 16px;
      font-weight: 600;
      color: rgb(37, 50, 67);
    }
    .rankings-data {
      .ranking-row {
        position: relative;
        margin-right: 1.4rem;
        padding-right: 1.4rem;
        &:after {
          content: '';
          display: block;
          position: absolute;
          background: #ceddf2;
          margin-right: 1.4rem;
          right: 0;
          top: 50%;
          height: 30px;
          width: 1px;
          z-index: 9999999;
          display: block;
          transform: translateY(-50%);
          @media (max-width: 768px) {
            display: none;
          }
        }
        .ranking-item {
          margin-right: 1.4rem;
          display: flex;
          flex-direction: column;
          width: 100%;
          .ranking-label {
            color: #5b7292;
            min-width: 100px;
            font-weight: 600;
          }
          .ranking-value {
            font-size: 2rem;
            min-width: 50px;
            font-weight: 600;
          }
        }
        .ranking-item {
          &:nth-child(2) {
            .ranking-label {
              min-width: 75px;
            }
          }
        }
      }
    }
    .position-type {
      .position-wrap {
        display: flex;
        border-radius: 4px;
        padding: 4px 6px;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        background: white;

        .position-label {
          color: #5b7292;
          font-weight: 600;
        }
        .position-direction {
          height: 30px;
          width: 30px;
          justify-content: center;
          display: flex;
          align-items: center;
          padding: 0.5rem;
          border-radius: 4px;

          &.positive {
            background: #30bf6040;
          }
          &.negative {
            background: #ff405040;
          }
        }
      }
    }
  }
`;
