import { ValueFormatterParams } from '@ag-grid-community/core';
import { absColDef, AdvancedColDef } from '../helpers/advancedHeader';
import { AbbreviationNumberFilter } from './AbbreviationNumberFilter';
import { isNilOrEmpty } from '@benzinga/utils';

export const CommaSeparatedNumberColDef = (colDef: AdvancedColDef): AdvancedColDef => ({
  cellStyle: { 'text-align': 'left' },

  // renderer handles
  comparator: (valueA: number, valueB: number) => {
    return Number(valueA) - Number(valueB);
  },

  filter: AbbreviationNumberFilter,
  valueFormatter: (data: ValueFormatterParams) => {
    const value = data.value;
    if (typeof value === 'number' && isFinite(value)) {
      return value.toLocaleString();
    }
    if (!isNilOrEmpty(value)) {
      return Number(value).toLocaleString();
    }
    return value ?? '';
  },
  valueGetter: params => {
    const num = Number(params.data?.[colDef.field ?? '']);
    return isNaN(num) ? null : num;
  },
  ...colDef,
  ...absColDef(colDef),
});
