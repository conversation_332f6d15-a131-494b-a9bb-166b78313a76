import { getCellValue } from '../helpers';
import { absColDef, AdvancedColDef } from '../helpers/advancedHeader';
import { comparators, isNilOrEmpty } from '@benzinga/utils';
import { AbbreviationNumberFilter } from './AbbreviationNumberFilter';

export const NumberColDef = (colDef: AdvancedColDef): AdvancedColDef => {
  const baseValueGetter = params => {
    const value = params.data[params.colDef.field ?? params.colDef.colId ?? ''];
    return !isNilOrEmpty(value) ? Number(value) : 0;
  };
  return {
    cellStyle: { 'text-align': 'right' },
    comparator: comparators.numberComparator,
    filter: AbbreviationNumberFilter,
    valueFormatter: params => getCellValue(params),
    valueGetter: baseValueGetter,
    ...colDef,
    ...absColDef(colDef),
  };
};
