import { IFilterParams, IDoesFilterPassParams, IFilterComp } from '@ag-grid-community/core';
import { numberShorthand, unshorthandValue } from '@benzinga/utils';
import { CSSProperties } from 'react';

interface HTMLConfig {
  tag: keyof JSX.IntrinsicElements;
  metadata?: { className?: string; placeholder?: string; type?: string; id?: string };
  style: CSSProperties;
}

export class AbbreviationNumberFilter implements IFilterComp {
  private input!: HTMLInputElement;
  private input2!: HTMLInputElement;
  private typeSelect!: HTMLSelectElement;
  private params!: IFilterParams;
  private rawInput = '';
  private rawInput2 = '';
  private filterType = 'equals';
  private gui!: HTMLElement;
  private betweenFilterContainer!: HTMLElement;
  private isProcessingFilter = false;
  private lastFocusedInput: HTMLInputElement | null = null;

  init(params: IFilterParams): void {
    this.params = params;
    this.gui = this.createGui();
  }

  getGui() {
    return this.gui;
  }

  valueEqualizer(cellValue: number, filterValue: string): boolean {
    const hasAbbreviation = filterValue.includes('k') || filterValue.includes('m') || filterValue.includes('b');
    if (hasAbbreviation) {
      const shortenedCellValue = numberShorthand(cellValue, 2).toLowerCase();
      return shortenedCellValue === filterValue.toLowerCase();
    }
    return cellValue === Number(filterValue);
  }

  doesFilterPass(params: IDoesFilterPassParams): boolean {
    if (this.isProcessingFilter) {
      return true;
    }

    this.isProcessingFilter = true;

    try {
      const rawValue =
        this.params.colDef.valueGetter && typeof this.params.colDef.valueGetter === 'function'
          ? this.params.colDef.valueGetter({
              ...this.params,
              ...params,
              getValue: (field: string) => params.data[field],
            })
          : params.data[this.params.colDef.field ?? this.params.colDef.colId ?? ''];

      if (['blank', 'notBlank'].includes(this.filterType)) {
        const empty = rawValue == null || rawValue === '';
        return this.filterType === 'blank' ? empty : !empty;
      }

      const trimmed = this.rawInput.replace(',', '').trim();
      if (!trimmed) return true;

      let cellValue: number;
      try {
        cellValue = Number(unshorthandValue(rawValue));
      } catch (error) {
        return true;
      }

      if (isNaN(cellValue)) return true;

      let filterValue: number;
      try {
        filterValue = Number(unshorthandValue(trimmed));
      } catch (error) {
        return true;
      }

      if (isNaN(filterValue)) return true;

      switch (this.filterType) {
        case 'equals':
          return this.valueEqualizer(cellValue, trimmed);
        case 'notEqual':
          return cellValue !== filterValue;
        case 'lessThan':
          return cellValue < filterValue;
        case 'lessThanOrEqual': {
          if (this.valueEqualizer(cellValue, trimmed)) return true;
          return cellValue <= filterValue;
        }
        case 'greaterThan':
          return cellValue > filterValue;
        case 'greaterThanOrEqual': {
          if (this.valueEqualizer(cellValue, trimmed)) return true;
          return cellValue >= filterValue;
        }
        case 'between': {
          if (this.rawInput.trim() === '' || this.rawInput2.trim() === '') {
            return true;
          }
          let filterValue2: number;
          try {
            filterValue2 = Number(unshorthandValue(this.rawInput2));
          } catch (error) {
            return true;
          }

          if (isNaN(filterValue2)) return true;

          return cellValue >= filterValue && cellValue <= filterValue2;
        }
        default:
          return true;
      }
    } finally {
      this.isProcessingFilter = false;
    }
  }

  isFilterActive(): boolean {
    if (this.filterType === 'blank' || this.filterType === 'notBlank') {
      return true;
    }
    if (this.filterType === 'between') {
      return (this.rawInput || '').trim() !== '' || (this.rawInput2 || '').trim() !== '';
    }
    return (this.rawInput || '').trim() !== '';
  }

  getModel() {
    if (!this.isFilterActive()) {
      return null;
    }

    if (this.filterType === 'blank' || this.filterType === 'notBlank') {
      return { type: this.filterType };
    }

    if (this.filterType === 'between') {
      return {
        type: this.filterType,
        value: this.rawInput,
        value2: this.rawInput2,
      };
    }

    return {
      type: this.filterType,
      value: this.rawInput,
    };
  }

  setModel(model): void {
    if (!model) {
      this.reset();
      return;
    }
    this.filterType = model.type || 'equals';
    this.typeSelect.value = this.filterType;

    this.rawInput = model.value ?? '';
    this.input.value = this.rawInput;

    this.rawInput2 = this.filterType === 'between' && model.value2 ? model.value2 : '';
    this.input2.value = this.rawInput2;

    this.updateInputVisibility();
  }

  private createHTMLElement(config: HTMLConfig) {
    const element = document.createElement(config.tag || 'div');
    Object.keys(config.style).forEach(key => {
      element.style[key] = config.style[key];
    });
    Object.keys(config.metadata || {}).forEach(key => {
      element[key] = config?.metadata?.[key];
    });
    return element;
  }

  private createGui(): HTMLElement {
    this.typeSelect = this.createHTMLElement({
      metadata: {
        className: 'ag-filter-select',
      },
      style: {
        backgroundColor: '#28313C',
        borderRadius: '4px',
        color: 'white',
        padding: '5px',
        width: '100%',
      },
      tag: 'select',
    }) as HTMLSelectElement;

    //filter type container
    const filterTypeContainer = this.createHTMLElement({
      metadata: {},
      style: {
        marginTop: '5px',
      },
      tag: 'div',
    });
    filterTypeContainer.appendChild(this.typeSelect);
    //container
    const container = this.createHTMLElement({
      metadata: {
        className: 'ag-filter-wrapper',
      },
      style: {
        marginBottom: '10px',
        paddingRight: '10px',
      },
      tag: 'div',
    });

    //input container
    const inputContainer = this.createHTMLElement({
      metadata: {
        id: 'mainFilterInput',
      },
      style: {
        marginTop: '5px',
      },
      tag: 'div',
    });

    this.input = this.createHTMLElement({
      metadata: {
        className: 'ag-filter-input',
        placeholder: 'Filter...',
        type: 'text',
      },
      style: {
        backgroundColor: '#28313C',
        color: 'white',
        padding: '5px',
        width: '100%',
      },
      tag: 'input',
    }) as HTMLInputElement;

    this.typeSelect.addEventListener('change', () => {
      this.filterType = this.typeSelect.value;
      this.updateInputVisibility();
      if (this.params) {
        this.params.filterChangedCallback();
      }
    });

    this.input.addEventListener('input', _e => {
      this.rawInput = this.input.value;
      this.lastFocusedInput = this.input;
      setTimeout(() => {
        this.params.filterChangedCallback();
        if (this.lastFocusedInput && document.activeElement !== this.lastFocusedInput) {
          this.lastFocusedInput.focus();
        }
      }, 0);
    });

    this.input.addEventListener('focus', () => {
      this.lastFocusedInput = this.input;
    });

    this.input.addEventListener('keydown', e => {
      if (e.key === 'Backspace' && this.input.value === '') {
        e.preventDefault();
      }
    });

    inputContainer.appendChild(this.input);

    const filterTypes = [
      { text: 'Equals', value: 'equals' },
      { text: 'Does not equal', value: 'notEqual' },
      { text: 'Greater than', value: 'greaterThan' },
      { text: 'Greater than or equal to', value: 'greaterThanOrEqual' },
      { text: 'Less than', value: 'lessThan' },
      { text: 'Less than or equal to', value: 'lessThanOrEqual' },
      { text: 'Between', value: 'between' },
      { text: 'Blank', value: 'blank' },
      { text: 'Not blank', value: 'notBlank' },
    ];

    //between filter
    this.betweenFilterContainer = this.createHTMLElement({
      style: {
        display: 'none',
        marginTop: '5px',
      },
      tag: 'div',
    });

    this.input2 = this.createHTMLElement({
      metadata: {
        className: 'ag-filter-input',
        placeholder: 'To...',
        type: 'text',
      },
      style: {
        backgroundColor: '#28313C',
        color: 'white',
        padding: '5px',
        width: '100%',
      },
      tag: 'input',
    }) as HTMLInputElement;

    this.input2.addEventListener('input', _e => {
      this.rawInput2 = this.input2.value;
      this.lastFocusedInput = this.input2;
      setTimeout(() => {
        this.params.filterChangedCallback();
        if (this.lastFocusedInput && document.activeElement !== this.lastFocusedInput) {
          this.lastFocusedInput.focus();
        }
      }, 0);
    });

    this.input2.addEventListener('focus', () => {
      this.lastFocusedInput = this.input2;
    });

    this.input2.addEventListener('keydown', e => {
      if (e.key === 'Backspace' && this.input2.value === '') {
        e.preventDefault();
      }
    });

    this.betweenFilterContainer.appendChild(this.input2);

    filterTypes.forEach(type => {
      const option = document.createElement('option');
      option.value = type.value;
      option.text = type.text;
      this.typeSelect.appendChild(option);
    });

    container.appendChild(filterTypeContainer);
    container.appendChild(inputContainer);
    container.appendChild(this.betweenFilterContainer);

    return container;
  }

  private updateInputVisibility(): void {
    const needsInput = !['blank', 'notBlank'].includes(this.filterType);
    const needsSecondInput = this.filterType === 'between';

    const mainInput = this.input.parentElement as HTMLElement;
    mainInput.style.display = needsInput ? 'block' : 'none';
    this.betweenFilterContainer.style.display = needsSecondInput ? 'block' : 'none';
  }

  private reset(): void {
    this.rawInput = '';
    this.rawInput2 = '';
    if (this.input) this.input.value = '';
    if (this.input2) this.input2.value = '';
    this.updateInputVisibility();
  }
}
