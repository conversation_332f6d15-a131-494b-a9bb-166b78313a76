'use client';
import React, { SVGAttributes, FC } from 'react';

import styled, { ThemeConsumer, Theme, TC } from '@benzinga/themetron';
import { StockSymbol, UnixTimestamp } from '@benzinga/session';
import { ChartInterval, ChartRange } from '@benzinga/chart-manager';

import { transparentize } from 'polished';
import saturate from 'polished/lib/color/saturate';
import { Area, AreaChart, Label, ReferenceLine, ResponsiveContainer, XAxis, YAxis } from 'recharts';
import { WidgetContext } from '@benzinga/widget-tools';
import { useChart } from '@benzinga/chart-manager-hooks';

const NBSP = '\xa0';
const DEFAULT_CHART_PARAMS: ChartParams = { from: '1d', interval: '2m' };

export const sparklineTimeFrames: (ChartParams & { label: string })[] = [
  { from: '1d', interval: '2m', label: '1 Day Chart' },
  { from: '2d', interval: '5m', label: '2 Day Chart' },
  { from: '5d', interval: '10m', label: '5 Day Chart' },
  { from: '1m', interval: '30m', label: '1 Month Chart' },
  { from: '90d', interval: '4h', label: '90 Day Chart' },
  { from: '180d', interval: '1d', label: '180 Day Chart' },
  { from: 'YTD', interval: '1d', label: 'YTD Chart' },
  { from: '1y', interval: '1d', label: '1 Year Chart' },
  { from: '2y', interval: '1w', label: '2 Year Chart' },
  { from: '5y', interval: '2w', label: '5 Year Chart' },
  { from: '10y', interval: '1mo', label: '10 Year Chart' },
];

export interface ChartParams {
  from: ChartRange;
  interval: ChartInterval;
  extendedSession?: boolean;
}

interface Props {
  referenceTime?: UnixTimestamp;
  symbol: StockSymbol;
  updateHeight?: () => void;
  omitTextInfo?: boolean;
  height?: 'compact' | 'full';
  chartParams?: ChartParams;
}

export const Sparkline: FC<Props> = ({
  chartParams = DEFAULT_CHART_PARAMS,
  height,
  omitTextInfo = false,
  referenceTime,
  symbol,
  updateHeight,
}) => {
  const [wasCached, setWasCached] = React.useState<boolean>(false);
  const chartData = useChart(
    symbol,
    chartParams.from,
    chartParams.interval,
    chartParams.extendedSession,
    undefined,
    () => {
      setWasCached(true);
      updateHeight?.();
    },
  );

  const widget = React.useContext(WidgetContext);

  const linearGradientId = React.useMemo(() => {
    if (typeof chartParams.from === 'string')
      return `${symbol}${chartParams.from}${chartParams.interval}${widget?.widgetId ?? ''}`;
    else return `${symbol}${chartParams.from.toISOString()}${chartParams.interval}${widget?.widgetId ?? ''}`;
  }, [chartParams.from, chartParams.interval, symbol, widget?.widgetId]);

  const getDataMinAndMax = React.useMemo((): [number, number] => {
    if (chartData?.candles === undefined) {
      return [0, 0];
    }
    const candles = chartData.candles;
    const candlesOpen = candles.map(data => data.open);
    const dataMin = Math.min(...candlesOpen);
    const dataMax = Math.max(...candlesOpen);

    return [dataMin, dataMax];
  }, [chartData]);

  const referenceTimeInt = React.useMemo<number>(
    () => (referenceTime ? parseInt(referenceTime, 10) : 0),
    [referenceTime],
  );

  // This function returns a value between 0 and 1 indicating the boundary between the green and red chart colors
  // We try to return the location of the firstClose in relation to dataMin and dataMax
  const getGradientOffset = React.useMemo(() => {
    if (chartData?.candles === undefined) {
      return 0;
    }

    const candles = chartData.candles;
    const [dataMin, dataMax] = getDataMinAndMax as [number, number];
    if ((candles?.length ?? 0) === 0) {
      return 0;
    }

    const candle =
      candles.find((candle, idx, arr) => {
        const startTime = parseInt(candle.time, 10);
        const endTime = parseInt(arr[idx + 1]?.time ?? '0', 10);
        return startTime <= referenceTimeInt && endTime >= referenceTimeInt;
      }) ?? candles[0];
    const candleOpen = candle.open;

    if (dataMax <= candleOpen) {
      return 0; // 100% red
    }
    if (dataMin >= candleOpen) {
      return 1; // 100% green
    }

    // This ensures that we don't divide by 0.
    // (If this even happens, there wouldn't really be anything to graph as it would be a straight line)
    if (dataMax - dataMin === 0) {
      return 0.5; // 50% red, 50% green
    }

    // This calculates the percentage of the chart between dataMax and dataMin that should be green/red.
    return (dataMax - candleOpen) / (dataMax - dataMin);
  }, [chartData?.candles, getDataMinAndMax, referenceTimeInt]);

  const getTimeMinAndMax = React.useMemo((): [number, number] => {
    if (chartData?.candles === undefined) {
      return [0, 0];
    }
    const candles = chartData.candles;
    if (candles.length === 0) {
      return [0, 0];
    }
    const timeMin = parseInt(candles[0].time, 10);
    const timeMax = parseInt(candles[candles.length - 1].time, 10);

    return [timeMin, timeMax];
  }, [chartData]);

  if (chartData?.candles === undefined) {
    return null;
  }

  const candles = chartData.candles;

  // We need two or more data points to have a useful chart.
  if (candles?.length < 2) {
    return null;
  }
  const yAxisDomain = getDataMinAndMax;
  const xAxisDomain = getTimeMinAndMax;
  const gradientOffset = getGradientOffset;

  const [{ open: firstOpen }] = candles;

  const [dataMin, dataMax] = yAxisDomain as [number, number];

  return (
    <RechartsContainerColumn compactView={omitTextInfo}>
      {!omitTextInfo && (
        <ChartLabelWrapperRow>
          <TC.Column>
            <ChartLabelRow>Timeframe</ChartLabelRow>
            <ChartStatRow>
              <NeutralRow>1 Day</NeutralRow>
            </ChartStatRow>
          </TC.Column>

          <TC.Column>
            <ChartLabelRow>Range</ChartLabelRow>
            <ChartStatRow>
              <NegativeRow>{dataMin}</NegativeRow>
              {NBSP}-{NBSP}
              <PositiveRow>{dataMax}</PositiveRow>
            </ChartStatRow>
          </TC.Column>

          <TC.Column>
            <ChartLabelRow>Open</ChartLabelRow>
            <ChartStatRow>
              <NeutralRow>{firstOpen}</NeutralRow>
            </ChartStatRow>
          </TC.Column>
        </ChartLabelWrapperRow>
      )}
      <ThemeConsumer>
        {(theme: Theme) => (
          <ResponsiveContainer height={height === 'compact' ? 20 : 40} width="100%">
            <AreaChart data={candles} margin={areaChartMargin}>
              <ReferenceLine
                stroke={transparentize(0.25, theme.colors.foregroundInactive)}
                strokeDasharray="1"
                strokeDashoffset="1"
                strokeWidth={0.5}
                y={firstOpen}
              />

              <ReferenceLine
                stroke={transparentize(0.25, theme.colors.statistic.positive)}
                strokeDasharray="1"
                strokeWidth={0.5}
                y={dataMax}
              />

              <ReferenceLine
                stroke={transparentize(0.25, theme.colors.statistic.negative)}
                strokeDasharray="1"
                strokeWidth={0.5}
                y={dataMin}
              />

              {referenceTimeInt && (
                <ReferenceLine stroke={theme.colors.brand} strokeWidth={2} x={referenceTimeInt}>
                  <Label position="bottom">event</Label>
                </ReferenceLine>
              )}

              <XAxis dataKey="time" domain={xAxisDomain} hide type="number" />
              <YAxis dataKey="close" domain={yAxisDomain} hide type="number" />
              <defs>
                <linearGradient id={`splitColor${linearGradientId}`} x1="0" x2="0" y1="0" y2="1">
                  <GreenStop offset={0} stopOpacity={0.5} />
                  <GreenStopEnd offset={gradientOffset} stopOpacity={0.05} />
                  <RedStopEnd offset={gradientOffset} stopOpacity={0.05} />
                  <RedStop offset={1} stopOpacity={0.5} />
                </linearGradient>

                <linearGradient id={`lineColor${linearGradientId}`} x1="0%" x2="0%" y1="0%" y2="100%">
                  <GreenStop offset={0} stopOpacity={1} />
                  <GreenStop offset={gradientOffset} stopOpacity={0.5} />
                  <RedStop offset={gradientOffset} stopOpacity={0.5} />
                  <RedStop offset={1} stopOpacity={1} />
                </linearGradient>
              </defs>
              <Area
                animationDuration={350}
                animationEasing="linear"
                dataKey="open"
                fill={`url(#splitColor${linearGradientId})`}
                isAnimationActive={!wasCached}
                stroke={`url(#lineColor${linearGradientId})`}
                strokeWidth={1.5}
              />
            </AreaChart>
          </ResponsiveContainer>
        )}
      </ThemeConsumer>
    </RechartsContainerColumn>
  );
};

const Stop: React.FC<SVGAttributes<SVGStopElement>> = ({ children, ...props }) => <stop {...props}>{children}</stop>;

const GreenStop = styled(Stop)`
  stop-color: ${props => props.theme.colors.statistic.positive};
`;

const GreenStopEnd = styled(Stop)`
  stop-color: ${props => saturate(0.5, props.theme.colors.statistic.positive)};
`;

const RedStop = styled(Stop)`
  stop-color: ${props => props.theme.colors.statistic.negative};
`;

const RedStopEnd = styled(Stop)`
  stop-color: ${props => saturate(0.5, props.theme.colors.statistic.negative)};
`;

const areaChartMargin = {
  bottom: 5,
  left: 0,
  right: 0,
  top: 5,
};

const RechartsContainerColumn = styled(TC.Column)<{ compactView: boolean }>`
  margin: ${props => (props.compactView ? 0 : '5px 10px')};
  /* height: ${props => (props.compactView ? 20 : 40)}; */
`;

const ChartLabelRow = styled(TC.Row)`
  font-size: 0.75em;
  padding-right: 0.35em;
`;

const ChartLabelWrapperRow = styled(TC.Row)`
  color: ${props => props.theme.colors.foregroundInactive};
  flex-wrap: wrap;
  font-size: 0.875em;
  font-weight: 300;
  justify-content: space-between;
  line-height: 1em;
  margin-bottom: -0.25em;
  margin-right: 0.25em;
`;

const ChartStatRow = styled(TC.Row)`
  font-size: 0.875em;
`;

const PositiveRow = styled(TC.Row)`
  color: ${props => props.theme.colors.statistic.positive};
`;

const NegativeRow = styled(TC.Row)`
  color: ${props => props.theme.colors.statistic.negative};
`;

const NeutralRow = styled(TC.Row)`
  color: ${props => props.theme.colors.foreground};
`;
