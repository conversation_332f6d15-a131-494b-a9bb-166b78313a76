'use client';
import React from 'react';
import styled from '@benzinga/themetron';

import { Layout, SidebarSettings } from '@benzinga/core-ui';
import {
  ContentFeed,
  FeaturedSection,
  NewsSection,
  DefaultNewsElement,
  Item,
  FeaturedSectionIndia,
} from '@benzinga/news';
import { toTitleCase } from '@benzinga/utils';
import { ContentBlock, WordpressSidebar, PostHeader, Term } from '@benzinga/content-manager';
import { StoryObject } from '@benzinga/basic-news-manager';
import { SimpleNewsQueryAndOptions, TidParam, PARTNER_CONTENT } from '@benzinga/internal-news-manager';
import { PageProps } from '../../entities';
import { CampaignPopup } from '../Campaign/CampaignPopup';
import { FloatingPopup } from '../Campaign/FloatingPopup';
import { MoneyBlocks } from '../MoneyBlocks';
import { MoneySidebar } from '../MoneySidebar';
import { NoFirstRender } from '@benzinga/hooks';
import { useSponsoredContentArticle } from '@benzinga/content-manager-hooks';
import { SessionContext } from '@benzinga/session-context';
import { NewsAlertsManager } from '@benzinga/news-alerts-manager';

import { LayoutAdmin } from '@benzinga/blocks';
import { useBenzingaEdge } from '@benzinga/edge';
import { appEnvironment, appName } from '@benzinga/utils';

const DefaultAdHeader = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.DefaultAdHeader };
  }),
);

const getRecentNewsParams = (
  term: Term,
  type: string | string[] = ['benzinga_reach', 'story'],
): SimpleNewsQueryAndOptions => {
  // TODO: WILL REMOVE LATER
  const isIsraelPage = term.tid === '21365';
  if (term.vid === '1') {
    return {
      channels: [term.tid],
      displayOutput: 'abstract',
      type,
    };
  } else if (term.vid === '3') {
    return {
      displayOutput: 'abstract',
      headlines: isIsraelPage ? 'include' : undefined,
      tags: [term.tid],
      type,
    };
  }
  return {};
};

interface PageLayout {
  campaigns?: any;
  header: PostHeader;
  above_content?: ContentBlock;
  in_content?: ContentBlock;
  below_content?: ContentBlock;
  sidebar: WordpressSidebar;
  below_main_content?: ContentBlock;
}

interface NewsTemplateProps extends PageProps {
  disableDefaultAd?: boolean;
  disableFollowChannelButton?: boolean;
  hideContentListTitle?: boolean;
  maxWidth?: string;
  news?: Item[];
  layout?: PageLayout;
  term?: Term;
  featuredNews?: StoryObject[];
  featuredSectionVariant?: string;
  layoutFooter?: React.ReactElement;
  layoutSidebar?: React.ReactElement;
  showCommentsIcon?: boolean;
  sidebarSettings?: SidebarSettings;
}

export const NewsTemplate: React.FC<NewsTemplateProps> = ({
  disableDefaultAd = false,
  disableFollowChannelButton = false,
  featuredNews = [],
  featuredSectionVariant = 'default',
  hideContentListTitle = false,
  layout,
  layoutFooter,
  layoutSidebar,
  maxWidth,
  news = [],
  newsQuery,
  post,
  showCommentsIcon,
  sidebarSettings,
  template,
  term,
  type,
}) => {
  const session = React.useContext(SessionContext);
  const [sponsoredArticles] = useSponsoredContentArticle(session);
  const [categoryId, setCategoryId] = React.useState<string | null>(null);
  const hasAdLight = useBenzingaEdge().adLightEnabled;
  const isMoneyApp = appEnvironment().isApp(appName.money);

  React.useEffect(() => {
    const getCategory = async tid => {
      const newsAlertsManager = session.getManager(NewsAlertsManager);
      const category = await newsAlertsManager.getCategoryIdFromTerm(tid);
      setCategoryId(category);
    };
    if (template === 'channel' && term) {
      getCategory(term.tid);
    }
  }, [session, template, term, term?.tid]);

  if (!term) {
    return null;
  }

  const featuredNewsIds = (featuredNews.length >= 7 ? featuredNews : featuredNews.slice(0, 3)).map(node => node.id);
  const query = getRecentNewsParams(term, newsQuery?.type);
  const tags = Array.isArray(query?.tags) ? query.tags : [query?.tags];
  const isPartnerContentQuery = tags?.includes(PARTNER_CONTENT.toString());
  const showSponsoredContent = isPartnerContentQuery ? false : true;
  const defaultPostCardProps = { showCommentsIcon };

  // TODO: WILL REMOVE LATER
  const isIsraelPage = term.tid === '21365';

  const generateContentFeedTitle = () => {
    let title = '';

    if (isIsraelPage) {
      title = 'Israel Real-time News';
    } else if (term.name === 'Dividends') {
      title = 'Dividend Trading Ideas';
    } else if (term.name === 'Earnings') {
      title = 'Earnings Trading Ideas';
    } else if (featuredNews.length && term.name !== 'News') {
      title = `${toTitleCase(term.name)} Recent News`;
    } else if (featuredNews.length && term.name === 'News') {
      title = 'Recent News';
    }

    return title;
  };

  const contentFeedTitle = generateContentFeedTitle();

  let pageTitle = post?.title ?? (term ? toTitleCase(term.name) : 'Unknown');
  if (term.name === 'Dividends' || term.name === 'Earnings') {
    pageTitle = '';
  }

  return (
    <NewsTemplateWrapper maxWidth={maxWidth}>
      <Layout
        categoryId={categoryId}
        layoutAbove={
          layout?.above_content ? (
            <MoneyBlocks blocks={layout.above_content.blocks} campaigns={layout?.campaigns} />
          ) : (
            false
          )
        }
        layoutBelow={
          layout?.below_content ? (
            <MoneyBlocks blocks={layout.below_content.blocks} campaigns={layout?.campaigns} />
          ) : (
            false
          )
        }
        layoutHeader={
          <>
            {layout?.header && <NoFirstRender>{<LayoutAdmin post={layout.header} />}</NoFirstRender>}
            {layout?.header ? (
              <MoneyBlocks blocks={layout.header.blocks} />
            ) : disableDefaultAd || isPartnerContentQuery ? null : (
              <React.Suspense fallback={<div />}>{<DefaultAdHeader />}</React.Suspense>
            )}
          </>
        }
        layoutHeaderOptions={layout?.header?.options}
        layoutMain={
          <div>
            <NoFirstRender>{post && <LayoutAdmin post={post} />}</NoFirstRender>
            {post && featuredNews.length ? <MoneyBlocks blocks={post.blocks} campaigns={layout?.campaigns} /> : null}
            {!isIsraelPage && !!featuredNews.length && (
              <div>
                {featuredSectionVariant === 'india' ? (
                  <div className="mb-12">
                    <FeaturedSectionIndia nodes={featuredNews} />
                  </div>
                ) : (
                  <>
                    <hr className="my-4 border-bzgray-500" />
                    <FeaturedSection nodes={featuredNews} />
                    <hr className="my-4" />
                    {featuredNews.length >= 7 && (
                      <div>
                        <NewsSection
                          enableScrollTo={true}
                          nodes={featuredNews.slice(3, 7)}
                          showCommentsIcon={showCommentsIcon}
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
            <NoFirstRender>{layout?.in_content && <LayoutAdmin post={layout?.in_content} />}</NoFirstRender>
            {layout?.in_content && featuredNews.length ? (
              <MoneyBlocks blocks={layout.in_content.blocks} campaigns={layout?.campaigns} />
            ) : null}
            <div className="my-3 mb-4" id="more-news">
              <ContentFeed
                contentId={contentFeedTitle}
                excludeIds={isIsraelPage ? undefined : featuredNewsIds}
                isInfinite={layout?.below_content || layoutFooter ? false : true}
                limit={20}
                loadMore={true}
                newsItemElement={(node, index) => {
                  return (
                    <DefaultNewsElement
                      key={`${node.id}-${index}`}
                      node={node as StoryObject}
                      postCardProps={
                        isIsraelPage
                          ? { ...defaultPostCardProps, dateType: 'relative', imagePreload: index < 6 }
                          : defaultPostCardProps
                      }
                    />
                  );
                }}
                nodes={news}
                poolLatest={isIsraelPage}
                poolLatestInterval={30000}
                query={getRecentNewsParams(term, type)}
                realtime={isIsraelPage}
                showSponsoredContent={showSponsoredContent}
                sponsoredNodes={sponsoredArticles}
                title={hideContentListTitle ? '' : contentFeedTitle}
              />
            </div>
            {layout?.below_main_content ? (
              <MoneyBlocks blocks={layout.below_main_content.blocks} campaigns={layout?.campaigns} />
            ) : null}
          </div>
        }
        layoutSidebar={
          layout?.sidebar ? (
            <React.Suspense>
              <MoneySidebar sidebar={layout.sidebar} />
            </React.Suspense>
          ) : (
            layoutSidebar
          )
        }
        showFollowChannelButton={!disableFollowChannelButton}
        sidebarSettings={sidebarSettings}
        term={term}
        title={pageTitle || contentFeedTitle}
      />
      {layoutFooter && (
        <div className="layout-footer primary">
          <div className="layout-container mx-auto">{layoutFooter}</div>
        </div>
      )}
      {layout?.campaigns && <FloatingPopup targets={layout.campaigns} />}
      {layout?.campaigns && (isMoneyApp || !hasAdLight) && <CampaignPopup targets={layout.campaigns} />}
    </NewsTemplateWrapper>
  );
};

export default NewsTemplate;

const NewsTemplateWrapper = styled.div<{ maxWidth?: string }>`
  ${({ maxWidth }) =>
    maxWidth &&
    `
    max-width: ${maxWidth};
    margin: auto;
  `}
  .layout-footer {
    .layout-container {
      max-width: 1100px;
    }
    &.primary {
      padding-top: 1rem;
      background-color: rgb(242, 248, 255);
      border-top: 1px solid rgb(184, 203, 230);
    }
  }
`;
