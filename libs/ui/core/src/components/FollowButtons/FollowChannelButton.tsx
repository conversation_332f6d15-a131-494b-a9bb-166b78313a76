'use client';

import React from 'react';
import toast from 'react-hot-toast';

import { AuthenticationManager } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { NewsAlertsManager } from '@benzinga/news-alerts-manager';
import styled from '@benzinga/themetron';

interface Props {
  categoryId: string;
  termName?: string;
}

export const FollowChannelButton: React.FC<Props> = ({ categoryId, termName }) => {
  const session = React.useContext(SessionContext);
  const newsAlertsManager = session.getManager(NewsAlertsManager);
  const isLoggedIn = session.getManager(AuthenticationManager).isLoggedIn();

  const handleFollow = async () => {
    if (!isLoggedIn) {
      toast.error(`Please log in to subscribe to ${termName ?? 'this'} content.`);
      return;
    }

    const subscribe = await newsAlertsManager.setCategoriesAlert(categoryId, { realtimeEmail: true });
    if (subscribe.ok) {
      toast.success(`You are now subscribed to ${termName ?? 'this'} content.`, {
        duration: 3000,
      });
    } else {
      toast.error('Failed to subscribe to the channel. Please try again later.');
    }
  };

  return (
    <ButtonWrapper onClick={handleFollow}>
      <button className="follow-topic-button">Get Alerts</button>
      <p>Receive alerts when new {termName} content is published.</p>
    </ButtonWrapper>
  );
};

const ButtonWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;

  .follow-topic-button {
    padding: 0.25rem 0.5rem;
    color: white;
    background-color: black;
    border-radius: 4px;
    font-size: 14px;
    margin-right: 0.5rem;
    white-space: nowrap;
    &:hover {
      background-color: ${({ theme }) => theme.colorPalette.gray800};
    }
  }

  p {
    margin: 0;
    font-size: 14px;
    color: ${({ theme }) => theme.colorPalette.gray500};
  }

  @media (max-width: 800px) {
    p {
      font-size: 12px;
      line-height: 14px;
    }
  }
`;
