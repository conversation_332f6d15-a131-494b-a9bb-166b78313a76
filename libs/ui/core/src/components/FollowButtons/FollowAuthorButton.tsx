'use client';

import React from 'react';
import { Tooltip } from '../Tooltip';
import toast from 'react-hot-toast';

import { AuthenticationManager } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { NewsAlertsManager } from '@benzinga/news-alerts-manager';
import { FollowAuthor } from '@benzinga/icons';
import styled from '@benzinga/themetron';

interface Props {
  authorName: string;
  authorUid: number;
  variant?: 'icon' | 'text';
  className?: string;
}

export const FollowAuthorButton: React.FC<Props> = ({ authorName, authorUid, className, variant = 'icon' }: Props) => {
  const session = React.useContext(SessionContext);
  const newsAlertsManager = session.getManager(NewsAlertsManager);
  const isLoggedIn = session.getManager(AuthenticationManager).isLoggedIn();

  const handleFollow = async () => {
    if (!isLoggedIn) {
      toast.error(`Please log in to subscribe to ${authorName}.`);
      return;
    }
    const res = await newsAlertsManager.createAuthorSubscription(authorUid, authorName);
    if (res.ok) {
      toast.success(`You are now subscribed to new articles from ${authorName}.`, {
        duration: 3000,
      });
    } else {
      toast.error('Failed to subscribe to the author. Please try again later.');
    }
  };

  return (
    <Container $variant={variant} className={className} onClick={handleFollow}>
      {variant === 'icon' ? (
        <Tooltip content="Follow Author">
          <FollowAuthor />
        </Tooltip>
      ) : (
        <span className="font-semibold">Follow</span>
      )}
    </Container>
  );
};

const Container = styled.div<{ $variant: 'icon' | 'text' }>`
  cursor: pointer;
  rect:first-child {
    fill: black;
  }

  &.large {
    background-color: black;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      transform: translateY(-4px);
    }
  }

  // for pro/mobile
  &.transparent {
    rect:first-child {
      fill: none;
    }
    border: 1px solid #373f49;
    border-radius: 4px;
  }
`;
