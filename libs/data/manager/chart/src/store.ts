import { StockSymbol } from '@benzinga/session';
import {
  ChartDataResponse,
  ChartInterval,
  ChartRange,
  CryptoChartMultiply,
  CryptoChartResponse,
  CryptoChartTime,
} from './entities';

interface ChartData {
  cacheTime: number;
  data: ChartDataResponse;
  extendedSession?: boolean;
  from: ChartRange;
  interval: ChartInterval;
  to?: ChartRange;
}

interface BarsData {
  cacheTime: number;
  date: ChartDataResponse;
  from: string;
  interval: string;
  to?: string;
}

interface CryptoChartData {
  cacheTime: number;
  data: CryptoChartResponse;
  from: string;
  multiply: CryptoChartMultiply;
  time: CryptoChartTime;
  to: string;
}

//extra requests made reason: not finding charts for MAX interval & failed tickers

export class ChartStore {
  private static readonly CACHE_TTL_MS = 30000;
  private charts: Map<StockSymbol, ChartData[]> = new Map();
  private cryptoCharts: Map<StockSymbol, CryptoChartData[]> = new Map();
  private bars: Map<StockSymbol, BarsData[]> = new Map();

  public getChartData = (
    symbol: StockSymbol,
    from: ChartRange,
    interval: ChartInterval,
    extendedSession?: boolean,
    to?: ChartRange,
  ): ChartDataResponse | undefined => {
    const chartData = this.charts.get(symbol);
    if (Array.isArray(chartData)) {
      const cleanedChartData = chartData.filter(val => {
        const cacheTimeDiff = Date.now() - val.cacheTime < ChartStore.CACHE_TTL_MS;
        return cacheTimeDiff;
      });
      this.charts.set(symbol, cleanedChartData);
      return cleanedChartData.find(
        val =>
          val.interval === interval && val.from === from && val.to === to && val.extendedSession === extendedSession,
      )?.data;
    } else {
      return undefined;
    }
  };

  public setChartData = (
    symbol: StockSymbol,
    from: ChartRange,
    interval: ChartInterval,
    data: ChartDataResponse,
    extendedSession?: boolean,
    to?: ChartRange,
  ): void => {
    const chartData = this.charts.get(symbol);
    if (chartData) {
      const cleanedChartData = chartData.filter(val => Date.now() - val.cacheTime < ChartStore.CACHE_TTL_MS);
      cleanedChartData.push({ cacheTime: Date.now(), data, extendedSession, from, interval, to });
      this.charts.set(symbol, cleanedChartData);
    } else {
      this.charts.set(symbol, [{ cacheTime: Date.now(), data, extendedSession, from, interval, to }]);
    }
  };

  public getCryptoChart = (
    symbol: StockSymbol,
    from: string,
    to: string,
    multiply: CryptoChartMultiply,
    time: CryptoChartTime,
  ): CryptoChartResponse | undefined => {
    const chartData = this.cryptoCharts.get(symbol);
    if (Array.isArray(chartData)) {
      const cleanedChartData = chartData.filter(val => Date.now() - val.cacheTime < ChartStore.CACHE_TTL_MS);
      this.cryptoCharts.set(symbol, cleanedChartData);
      return cleanedChartData.find(
        val => val.from === from && val.to === to && val.multiply === multiply && val.time === time,
      )?.data;
    } else {
      return undefined;
    }
  };

  public setCryptoChart = (
    symbol: StockSymbol,
    from: string,
    to: string,
    multiply: CryptoChartMultiply,
    time: CryptoChartTime,
    data: CryptoChartResponse,
  ): void => {
    const cryptoChartData = this.cryptoCharts.get(symbol);
    if (cryptoChartData) {
      const cleanedData = cryptoChartData.filter(val => Date.now() - val.cacheTime < ChartStore.CACHE_TTL_MS);
      cleanedData.push({ cacheTime: Date.now(), data, from, multiply, time, to });
      this.cryptoCharts.set(symbol, cleanedData);
    } else {
      this.cryptoCharts.set(symbol, [{ cacheTime: Date.now(), data, from, multiply, time, to }]);
    }
  };

  public getBarsData = (
    symbol: StockSymbol,
    from: string,
    interval: string,
    to?: string,
  ): ChartDataResponse | undefined => {
    const barsData = this.bars.get(symbol);
    if (Array.isArray(barsData)) {
      const cleanedBarsData = barsData.filter(val => Date.now() - val.cacheTime < ChartStore.CACHE_TTL_MS);
      this.bars.set(symbol, cleanedBarsData);
      return cleanedBarsData.find(val => val.interval === interval && val.from === from && val.to === to)?.date;
    } else {
      return undefined;
    }
  };

  public setBarsData = (
    symbol: StockSymbol,
    from: string,
    interval: string,
    date: ChartDataResponse,
    to?: string,
  ): void => {
    const barsData = this.bars.get(symbol);
    if (barsData) {
      const cleanedData = barsData.filter(val => Date.now() - val.cacheTime < ChartStore.CACHE_TTL_MS);
      cleanedData.push({ cacheTime: Date.now(), date, from, interval, to });
      this.bars.set(symbol, cleanedData);
    } else {
      this.bars.set(symbol, [{ cacheTime: Date.now(), date, from, interval, to }]);
    }
  };
}
