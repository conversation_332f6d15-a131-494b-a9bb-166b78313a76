import { SafePromise } from '@benzinga/safe-await';
import { ExtendedSubscribable, Subscription } from '@benzinga/subscribable';
import { Session, StockSymbol } from '@benzinga/session';
import {
  Candle,
  ChartDataResponse,
  ChartInterval,
  ChartOptions,
  ChartRange,
  CryptoChartMultiply,
  CryptoChartResponse,
  CryptoChartTime,
} from './entities';
import { ChartStore } from './store';
import { ChartRequest, ChartRestfulEvent } from './request';
import { BarSocket } from './socket/socket';
export interface ChartUpdateEvent {
  data: ChartDataResponse;
  from: ChartRange;
  interval: ChartInterval;
  to?: ChartRange;
  type: 'chart_updated';
}

export interface CryptoChartUpdateEvent {
  data: CryptoChartResponse;
  from: string;
  multiply: CryptoChartMultiply;
  symbol: StockSymbol;
  time: CryptoChartTime;
  to: string;
  type: 'crypto_chart_updated';
}

export interface BarsUpdateEvent {
  data: ChartDataResponse;
  from: string;
  interval: string;
  type: 'bars_updated';
}

export type ChartManagerEvent = ChartRestfulEvent | ChartUpdateEvent | CryptoChartUpdateEvent | BarsUpdateEvent;

interface ChartFunctions {
  getChart: ChartManager['getChart'];
}

export class ChartManager extends ExtendedSubscribable<ChartManagerEvent, ChartFunctions> {
  private store: ChartStore;
  private session: Session;
  private request: ChartRequest;
  private requestSubscription?: Subscription<ChartRequest>;
  private barSocket: BarSocket | undefined;
  private barSocketSubscription?: SafePromise<boolean>;
  private notSupportedSymbols: StockSymbol[] = [];

  constructor(session: Session) {
    super();
    this.session = session;
    this.store = new ChartStore();
    this.request = new ChartRequest(session);
  }

  public static getName = () => 'benzinga-chart';

  public getStore = () => this.store;

  public getChart = async (options: ChartOptions): SafePromise<ChartDataResponse> => {
    const { extendedSession, from, interval, symbol, to } = options;

    const cachedData = this.store.getChartData(symbol, from, interval, extendedSession, to);
    if (cachedData === undefined && !this.notSupportedSymbols.includes(symbol)) {
      const chart = await this.request.getChart(options);
      if (chart.ok) {
        this.store.setChartData(symbol, from, interval, chart.ok, extendedSession, to);
        this.dispatch({
          data: chart.ok,
          from,
          interval,
          to,
          type: 'chart_updated',
        });
      } else {
        this.notSupportedSymbols.push(symbol);
      }
      return chart;
    }
    if (cachedData === undefined) {
      return {
        err: {
          data: undefined,
          message: 'No data available for this symbol',
          name: 'get_chart_error',
          type: 'get_chart_error',
        },
      };
    }
    this.dispatch({
      data: cachedData,
      from,
      interval,
      to,
      type: 'chart_updated',
    });
    return { ok: cachedData };
  };

  public getCryptoChart = async (
    symbol: StockSymbol,
    from: string,
    to: string,
    multiply: CryptoChartMultiply,
    time: CryptoChartTime,
  ): SafePromise<CryptoChartResponse> => {
    const cachedData = this.store.getCryptoChart(symbol, from, to, multiply, time);
    if (cachedData === undefined && !this.notSupportedSymbols.includes(symbol)) {
      const chart = await this.request.getCryptoChart(symbol, from, to, multiply, time);
      if (chart.ok) {
        this.store.setCryptoChart(symbol, from, to, multiply, time, chart.ok);
        this.dispatch({
          data: chart.ok,
          from,
          multiply,
          symbol,
          time,
          to,
          type: 'crypto_chart_updated',
        });
      }
      return chart;
    }
    if (cachedData === undefined) {
      return {
        err: {
          data: undefined,
          message: 'No data available for this symbol',
          name: 'get_crypto_chart_error',
          type: 'get_crypto_chart_error',
        },
      };
    }
    this.dispatch({
      data: cachedData,
      from,
      multiply,
      symbol,
      time,
      to,
      type: 'crypto_chart_updated',
    });
    return { ok: cachedData };
  };

  public getBars = async (
    symbol: string,
    params: { to?: string; from: string; interval: string },
  ): SafePromise<ChartDataResponse> => {
    const cachedData = this.store.getBarsData(symbol, params.from, params.interval, params.to);
    if (cachedData === undefined && !this.notSupportedSymbols.includes(symbol)) {
      const bars = await this.request.getBars(symbol, params);
      if (bars.ok) {
        this.store.setBarsData(symbol, params.from, params.interval, bars.ok);
        this.dispatch({
          data: bars.ok,
          from: params.from,
          interval: params.interval,
          type: 'bars_updated',
        });
      }
      return bars;
    }
    if (cachedData === undefined) {
      return {
        err: {
          data: undefined,
          message: 'No data available for this symbol',
          name: 'get_chart_error',
          type: 'get_chart_error',
        },
      };
    }
    this.dispatch({ data: cachedData, from: params.from, interval: params.interval, type: 'bars_updated' });
    return { ok: cachedData };
  };

  public getBarFromSocket = async (
    symbol: string,
    resolution: string,
    from: number,
    to: number,
    countBack?: number,
    /**
     * Used to identify if it's the first call of getBars
     */
    firstDataRequest?: boolean,
  ) => {
    if (!this.barSocket) {
      this.barSocket = new BarSocket(this.session);
      this.barSocketSubscription = this.barSocket.connect();
    }
    if (this.barSocketSubscription) {
      await this.barSocketSubscription;
      this.barSocketSubscription = undefined;
    }
    return this.barSocket.getBars(symbol, resolution, from, to, countBack, firstDataRequest);
  };

  public isPublicComSupported = async (symbol: string): Promise<boolean> => {
    const { default: tickers } = await import('./PublicComAllowedTickers');

    return tickers?.includes(symbol);
  };

  public getEmbedChart = async (symbol: StockSymbol, timeframe: ChartRange): SafePromise<{ data: Candle[] }> => {
    const chart = await this.request.getEmbedChart(symbol, timeframe);
    return chart;
  };

  protected onSubscribe(): ChartFunctions {
    return {
      getChart: this.getChart,
    };
  }

  protected onFirstSubscription(): void {
    this.requestSubscription = this.request.listen(event => this.dispatch(event));
  }

  protected onZeroSubscriptions(): void {
    this.requestSubscription?.unsubscribe();
  }
}
