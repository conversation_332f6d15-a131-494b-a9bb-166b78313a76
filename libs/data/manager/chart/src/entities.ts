import { StockSymbol } from '@benzinga/session';

export type ChartInterval =
  | '1m'
  | '2m'
  | '5m'
  | '10m'
  | '20m'
  | '20min'
  | '30m'
  | '2h'
  | '4h'
  | '6h'
  | '12h'
  | '1d'
  | '2d'
  | '5d'
  | '10d'
  | '1w'
  | '2w'
  | '1M'
  | '1mo';

export type ChartRange =
  | '1d'
  | '2d'
  | '5d'
  | '1m'
  | '90d'
  | '180d'
  | '3m'
  | '5m'
  | '6m'
  | 'YTD'
  | '1y'
  | '2y'
  | '5y'
  | '10y'
  | '50y'
  | Date;

export type ChartType = 'candlestick' | 'line';

interface ChartConfigItem {
  from: ChartRange;
  interval: ChartInterval;
  refreshRate: number;
}

export type ChartConfig = Record<Exclude<ChartRange, Date>, ChartConfigItem>;

export const MAX_DATE_FROM = new Date('1970-01-15');

export const chartConfig: ChartConfig = {
  // 365 = (365 / 5) * 5
  '10y': { from: '10y', interval: '10d', refreshRate: 360000 },

  /* eslint-disable sort-keys */
  '180d': { from: '180d', interval: '1d', refreshRate: 360000 },

  /* eslint-disable sort-keys */
  '1d': { from: '1d', interval: '2m', refreshRate: 40000 },

  // 360 = (24 * (60 / 20)) * 5
  '1m': { from: '1m', interval: '2h', refreshRate: 90000 },

  // 365
  '1y': { from: '1y', interval: '1d', refreshRate: 360000 },

  /* eslint-disable sort-keys */
  '2d': { from: '2d', interval: '5m', refreshRate: 40000 },

  // 365
  '2y': { from: '2y', interval: '2d', refreshRate: 360000 },

  // 372 = (24 /2) * 31
  '3m': { from: '3m', interval: '6h', refreshRate: 360000 },

  /* eslint-disable sort-keys */
  '50y': { from: '50y', interval: '1mo', refreshRate: 360000 },

  // 720 = (60 / 2) * 24
  '5d': { from: '5d', interval: '20m', refreshRate: 60000 },

  '5m': { from: '5m', interval: '1d', refreshRate: 360000 },

  // 365 = (365 / 2) * 2
  '5y': { from: '5y', interval: '5d', refreshRate: 360000 },

  // 372 = (24 / 6) * (31 * 3)
  '6m': { from: '6m', interval: '12h', refreshRate: 360000 },

  '90d': { from: '90d', interval: '4h', refreshRate: 360000 },
  // 372 = (24 / 12) * (31 * 3)
  YTD: { from: 'YTD', interval: '1d', refreshRate: 360000 }, // 1274 = (52 / 2) * 49
  /* eslint-enable sort-keys */
};

export const availableChartRanges: ChartRange[] = ['1d', '5d', '1m', '3m', '6m', 'YTD', '1y', '2y', '5y', '10y'];

export interface Candle {
  close: number;
  dateTime: string;
  high: number;
  low: number;
  open: number;
  time: string;
  volume: number;
}

export interface ChartDataResponse {
  candles?: Candle[];
  interval: number;
  symbol: StockSymbol;
  noData?: boolean;
}

export type ChartData = ChartDataResponse;

export type CryptoChartTime = 'minute' | 'hour' | 'day' | 'week' | 'month';

export type CryptoChartMultiply = 1 | 3 | 5 | 15 | 30;

export interface FinageCryptoAggregatesData {
  symbol: string;
  totalResults: number;
  results: {
    o: number; // Open price
    h: number; // High price
    l: number; // Low price
    c: number; // Close price
    v: number; // Volume
    t: number; // Timestamp [ms]
  }[];
  error?: string;
}

export interface CryptoChartResponseResult {
  close: number;
  high: number;
  low: number;
  open: number;
  volume: number;
  x: string;
  y: number;
}

export interface CryptoChartResponse {
  results?: CryptoChartResponseResult[];
  symbol: string;
  totalResults: number;
  error?: string;
}

export interface ChartOptions {
  symbol: StockSymbol;
  from: ChartRange;
  to?: ChartRange;
  interval: ChartInterval;
  extendedSession?: boolean;
  apiKey?: string;
}
