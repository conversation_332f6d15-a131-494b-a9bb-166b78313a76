import { StockSymbol } from '@benzinga/session';
import { DateTime } from 'luxon';
import { ColDef, CellClassParams, ValueGetterParams, ColGroupDef } from '@ag-grid-community/core';

import {
  BooleanColDef,
  CalculatedChangePercentColDef,
  ChangePercentColDef,
  CommaSeparatedNumberColDef,
  DateColDef,
  DateColDefTimeSettings,
  DateTimeColDef,
  FlagColDef,
  LinkColDef,
  NO_VALUE,
  NumberColDef,
  PercentNumberColDef,
  PeriodColDef,
  PriceColDef,
  ProgressColDef,
  SetColDef,
  SymbolColDef,
  TextColDef,
  comparators,
  marketTime,
} from '@benzinga/pro-ui';

import { CalendarType, DateDaysOffset } from '@benzinga/calendar-manager-hooks';
import { addTutorialClasses } from '@benzinga/frontend-utils';
import { Fda } from '@benzinga/calendar-fda-manager';
import { getTimeDisplayFormat } from '@benzinga/date-utils';
import { Squawk } from '@benzinga/calendar-squawk-manager';
import { QuotesManager } from '@benzinga/quotes-manager';
import { isNotNil } from '@benzinga/utils';

export const calendarOrder = [
  'conference',
  'dividends',
  'earnings',
  'fda',
  'optionsActivity',
  'economics',
  'governmentTrades',
  'guidance',
  'ipos',
  'ma',
  'offerings',
  'ratings',
  'sec',
  // 'shortInterest',
  'splits',
  'squawk',
];

export const calendarLinkContextColIds = {
  conference: ['ticker'],
  dividends: ['ticker'],
  earnings: ['ticker'],
  economics: [],
  fda: ['ticker'],
  governmentTrades: ['security.ticker'],
  guidance: ['ticker'],
  ipos: ['ticker'],
  ma: ['acquirer_ticker', 'target_ticker'],
  offerings: ['ticker'],
  optionsActivity: ['ticker'],
  ratings: ['ticker'],
  sec: ['ticker'],
  shortInterest: ['symbol'],
  splits: ['ticker'],
  squawk: [],
};

export interface CalendarDefinition {
  defaultColDef: ColDef;
  defaultDates: {
    nothingSearched: DateDaysOffset;
    somethingSearched: DateDaysOffset;
  };
  name: string;
}

export const calendarTooltips = {
  optionsActivity: {
    ask: 'Ask',
    bid: 'Bid',
    cost_basis: 'Total Trade Price',
    date: 'Date',
    date_expiration: 'Option Date of Expiration',
    description: 'Readable description of signal',
    description_extended: 'Readable extended description of signal',
    dte: 'Days to Expiration',
    itm_otm: 'In the Money / Out of the Money',
    midpoint: 'Midpoint',
    open_interest: 'Open Interest',
    option_activity_type: 'Option Trade or Sweep',
    option_symbol: 'Underlying Options Symbol',
    price: 'Trade or Price of Last Trade in Sweep',
    put_call: 'PUT or Call Option Trade',
    sentiment: 'Bullish / Bearish indicator for Options signal',
    size: 'Total Order Size (1 trade, or sum of sweep)',
    strike_price: 'Option Strike Price',
    ticker: 'Symbol of Underlying Security',
    time: 'Time of Trade',
    trade_count: 'Number of Trades in Option Activity',
    underlying_price: 'The Underlying Price',
    underlying_type: 'Security Type of Stock or ETF',
    updated: 'Time of Trade',
    volOI: 'Volume over Open Interest',
    volume: 'Option Total Day Volume at Trade',
  },
};

type UOAFields = keyof (typeof calendarTooltips)['optionsActivity'];
const getCalendarTooltip = (field: UOAFields): string => calendarTooltips['optionsActivity'][field];

const conferenceColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Call Date',
        },
        {
          field: 'start_time',
          headerName: 'Start Time',
        },
        timeSettings,
      ),

      TextColDef({
        field: 'name',
        headerName: 'Name',
      }),
      LinkColDef({
        field: 'webcast_url',
        headerName: 'Webcast',
      }),
      TextColDef({
        field: 'phone_num',
        headerName: 'Phone Number',
      }),
      TextColDef({
        field: 'international_num',
        headerName: 'International Number',
      }),
      TextColDef({
        field: 'access_code',
        headerName: 'Access Code',
      }),
      ProgressColDef(
        {
          field: 'importance',
          headerName: 'Importance',
          initialHide: true,
        },
        { high: 5, low: 1 },
      ),
    ],
    headerName: 'Conference Call',
  },
];

const dividendsColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
    initialPinned: true,
  }),
  {
    children: [
      DateColDef(
        {
          field: 'ex_dividend_date',
          headerName: 'Ex-Date',
          headerTooltip: 'A purchase on that date (or after) will be ex (outside, without right to) the dividend',
        },
        timeSettings,
      ),
      TextColDef({
        field: 'name',
        headerName: 'Name',
      }),
      NumberColDef({
        field: 'dividend',
        headerName: 'Dividend',
      }),
      PercentNumberColDef(
        {
          field: 'dividend_yield',
          headerName: 'Yield',
        },
        100,
      ),
      DateColDef(
        {
          field: 'date',
          headerName: 'Announced',
        },
        timeSettings,
      ),
      DateColDef(
        {
          field: 'record_date',
          headerName: 'Record',
        },
        timeSettings,
      ),
      DateColDef(
        {
          field: 'payable_date',
          headerName: 'Payable',
        },
        timeSettings,
      ),
      NumberColDef({
        field: 'frequency',
        headerName: 'Frequency',
      }),
    ],
    headerName: 'Dividends',
  },
];

const earningsColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
    initialPinned: true,
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Date Announced',
        },
        {
          field: 'time',
          headerName: 'Time Announced',
          initialHide: true,
        },
        timeSettings,
      ),
      TextColDef({
        field: 'timeFrame',
        headerName: 'Timeframe',
        valueGetter: ({ data }: ValueGetterParams) =>
          marketTime(data.time, {
            afterMarket: 'After Market Close',
            invalid: null,
            preMarket: 'Before Market Open',
            regular: 'Intraday',
          }),
      }),
      TextColDef({
        field: 'name',
        headerName: 'Name',
      }),
      PeriodColDef({
        field: 'period',
        headerName: 'Period',
      }),
      TextColDef({
        field: 'period_year',
        headerName: 'Period Year',
        initialHide: true,
      }),
      BooleanColDef(
        {
          field: 'date_confirmed',
          headerName: 'Confirmed Date',
        },
        'Unconfirmed',
        'Confirmed',
      ),
      NumberColDef({
        field: 'eps',
        headerName: 'EPS',
      }),
      NumberColDef({
        field: 'eps_est',
        headerName: 'Estimated EPS',
      }),
      TextColDef({
        field: 'EPS_TYPE',
        headerName: 'EPS Type',
        valueGetter: ({ data }: ValueGetterParams) => data.eps_type?.toUpperCase() || NO_VALUE,
      }),
      CalculatedChangePercentColDef(
        {
          field: 'rev_growth_yoy',
          headerName: 'Revenue Growth (YOY %)',
        },
        'revenue_prior',
        'revenue',
      ),
      CalculatedChangePercentColDef(
        {
          field: 'surprise_pct_eps',
          headerName: 'Surprise % EPS',
        },
        'eps_est',
        'eps',
      ),
      CalculatedChangePercentColDef(
        {
          field: 'eps_growth_yoy',
          headerName: 'EPS Growth (YOY %)',
        },
        'eps_prior',
        'eps',
      ),
      NumberColDef({
        field: 'revenue',
        headerName: 'Revenue',
      }),
      TextColDef({
        field: 'revenue_type',
        headerName: 'Revenue Type',
        valueGetter: ({ data }: ValueGetterParams) => data.revenue_type?.toUpperCase() || NO_VALUE,
      }),
      NumberColDef({
        field: 'revenue_est',
        headerName: 'Estimated Revenue',
      }),
      CalculatedChangePercentColDef(
        {
          field: 'revenue_pct_revenue',
          headerName: 'Surprise % Revenue',
        },
        'revenue_est',
        'revenue',
      ),
      ProgressColDef(
        {
          field: 'importance',
          headerName: 'Importance',
        },
        { high: 5, low: 1 },
      ),
      NumberColDef({
        field: 'refinitiv_gps',
        headerName: 'Refinitiv GPS',
      }),
      NumberColDef({
        field: 'refinitiv_cps',
        headerName: 'Refinitiv CPS',
      }),
      NumberColDef({
        field: 'refinitiv_grm',
        headerName: 'Refinitiv GRM',
      }),
      NumberColDef({
        field: 'refinitiv_ffo',
        headerName: 'Refinitiv FFO',
      }),
      NumberColDef({
        field: 'implied_move_dollar',
        headerName: 'Implied Move (Dollar)',
      }),
      PercentNumberColDef(
        {
          field: 'implied_move_percent',
          headerName: 'Implied Move (%)',
        },
        1,
      ),
      NumberColDef({
        field: 'atm_strike',
        headerName: 'ATM Strike',
      }),
      DateColDef(
        {
          field: 'expiration_date',
          headerName: 'Expiration Date',
        },
        timeSettings,
      ),
      TextColDef({
        field: 'foot_notes',
        headerName: 'Foot Notes',
        valueGetter: ({ data }: ValueGetterParams) =>
          data.footnotes?.length > 0 ? data.footnotes.join(', ') : NO_VALUE,
      }),
    ],
    headerName: 'Earnings',
  },
];

const fdaColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    colId: 'ticker',
    headerName: 'Symbol',
    initialPinned: true,
    valueGetter: ({ data }: ValueGetterParams) =>
      (data as Fda).ticker ||
      (data as Fda).companies?.flatMap(c =>
        c?.securities?.reduce<StockSymbol[]>(
          (tickers, security) => (security?.symbol ? [...tickers, security.symbol] : tickers),
          [],
        ),
      ),
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Announcement Date',
          headerTooltip: getCalendarTooltip('date'),
          initialHide: true,
        },
        {
          field: 'time',
          headerName: 'Time',
          headerTooltip: getCalendarTooltip('time'),
          initialHide: true,
        },
        timeSettings,
      ),
      TextColDef({
        field: 'event_type',
        headerName: 'Event Type',
      }),
      TextColDef({
        field: 'companies_name',
        headerName: 'Companies',
        valueGetter: ({ data }: ValueGetterParams) => (data as Fda).companies?.map(company => company.name),
      }),
      TextColDef({
        field: 'drug_name',
        headerName: 'Drug',
        valueGetter: ({ data: { drug } }: ValueGetterParams) => drug?.name,
      }),
      TextColDef({
        field: 'status',
        headerName: 'Drug Status',
      }),
      DateColDef(
        {
          field: 'target_date',
          headerName: 'Target Date',
        },
        timeSettings,
      ),
      TextColDef({
        field: 'outcome',
        headerName: 'Significance/outcome',
      }),
      TextColDef({
        field: 'source_type',
        headerName: 'Source Type',
      }),
      LinkColDef({
        field: 'source_link',
        headerName: 'Source Link',
      }),
    ],
    headerName: 'FDA',
  },
];

const optionsActivityDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
    headerTooltip: getCalendarTooltip('ticker'),
    initialPinned: true,
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Date',
          headerTooltip: getCalendarTooltip('date'),
        },
        {
          field: 'time',
          headerName: 'Time',
          headerTooltip: getCalendarTooltip('time'),
        },
        timeSettings,
      ),
      SetColDef(
        {
          field: 'put_call',
          headerName: 'PUT/CALL',
          headerTooltip: getCalendarTooltip('put_call'),
        },
        ['PUT', 'CALL'],
      ),
      SetColDef(
        {
          cellClassRules: {
            'bz-ag-grid-downText': ({ value }) => value === 'OTM',
            'bz-ag-grid-upText': ({ value }) => value === 'ITM',
          },
          field: 'itm_otm',
          headerName: 'ITM/OTM',
          headerTooltip: getCalendarTooltip('itm_otm'),
          valueGetter: ({ data: { put_call, strike_price, underlying_price } }: ValueGetterParams) => {
            const diff = put_call === 'PUT' ? strike_price - underlying_price : underlying_price - strike_price;
            return !diff ? 'ATM' : diff > 0 ? 'ITM' : 'OTM';
          },
        },
        ['ITM', 'OTM'],
      ),
      PriceColDef({
        field: 'strike_price',
        headerName: 'Strike Price',
        headerTooltip: getCalendarTooltip('strike_price'),
      }),
      DateColDef(
        {
          field: 'date_expiration',
          headerName: 'Expiration Date',
          headerTooltip: getCalendarTooltip('date_expiration'),
        },
        timeSettings,
      ),
      NumberColDef({
        field: 'size',
        headerName: 'Size',
        headerTooltip: getCalendarTooltip('size'),
      }),
      NumberColDef({
        field: 'cost_basis',
        headerName: 'Trade Value',
        headerTooltip: getCalendarTooltip('cost_basis'),
      }),
      NumberColDef({
        cellClassRules: {
          'u-downText': ({ data: { midpoint }, value }: CellClassParams) => value < midpoint,
          'u-upText': ({ data: { midpoint }, value }: CellClassParams) => value > midpoint,
        },
        field: 'price',
        headerName: 'Premium',
        headerTooltip: getCalendarTooltip('price'),
      }),
      TextColDef({
        field: 'option_symbol',
        headerName: 'Option Symbol',
        headerTooltip: getCalendarTooltip('option_symbol'),
        initialHide: true,
      }),
      SetColDef(
        {
          cellClassRules: {
            'bz-ag-grid-downText': ({ value }) => value === 'BEARISH',
            'bz-ag-grid-upText': ({ value }) => value === 'BULLISH',
          },
          field: 'sentiment',
          headerName: 'Sentiment',
          headerTooltip: getCalendarTooltip('sentiment'),
          initialHide: true,
        },
        ['BULLISH', 'BEARISH', 'NEUTRAL'],
      ),
      SetColDef(
        {
          field: 'option_activity_type',
          headerName: 'Trade Type',
          headerTooltip: getCalendarTooltip('option_activity_type'),
          initialHide: true,
        },
        ['TRADE', 'SWEEP'],
      ),
      NumberColDef({
        field: 'dte',
        headerName: 'DTE',
        headerTooltip: getCalendarTooltip('dte'),
        initialHide: true,
        valueGetter: ({ data: { date, date_expiration } }: ValueGetterParams) => {
          const expDate = DateTime.fromISO(date_expiration);
          const lastTrade = DateTime.fromISO(date);
          const dte = expDate.diff(lastTrade, 'days').days;

          return dte <= 0 ? 0 : dte;
        },
      }),
      NumberColDef({
        field: 'bid',
        headerName: 'Bid',
        headerTooltip: getCalendarTooltip('bid'),
        initialHide: true,
      }),
      NumberColDef({
        field: 'ask',
        headerName: 'Ask',
        headerTooltip: getCalendarTooltip('ask'),
        initialHide: true,
      }),
      NumberColDef({
        field: 'midpoint',
        headerName: 'Midpoint',
        headerTooltip: getCalendarTooltip('midpoint'),
        initialHide: true,
      }),
      NumberColDef({
        field: 'trade_count',
        headerName: 'Trade Count',
        headerTooltip: getCalendarTooltip('trade_count'),
        initialHide: true,
      }),
      PriceColDef({
        field: 'underlying_price',
        headerName: 'Underlying Price',
        headerTooltip: getCalendarTooltip('underlying_price'),
        initialHide: true,
      }),
      SetColDef(
        {
          field: 'underlying_type',
          headerName: 'Security Type',
          headerTooltip: getCalendarTooltip('underlying_type'),
          initialHide: true,
        },
        ['ETF', 'STOCK'],
      ),
      CommaSeparatedNumberColDef({
        field: 'open_interest',
        headerName: 'OI',
        headerTooltip: getCalendarTooltip('open_interest'),
        initialHide: true,
        sortable: true,
      }),
      CommaSeparatedNumberColDef({
        field: 'volume',
        headerName: 'Volume At Trade',
        headerTooltip: getCalendarTooltip('volume'),
        initialHide: true,
        sortable: true,
      }),
      NumberColDef({
        field: 'volOI',
        headerName: 'Vol/OI',
        headerTooltip: getCalendarTooltip('volOI'),
        initialHide: true,
        valueGetter: ({ data: { open_interest, size } }) => (Number(size) / Number(open_interest)).toFixed(2),
      }),
      DateColDef(
        {
          field: 'updated',
          headerName: 'Last Trade',
          headerTooltip: getCalendarTooltip('updated'),
          initialHide: true,
          valueGetter: ({ data: { date, time } }: ValueGetterParams) => {
            const format = getTimeDisplayFormat({ timeFormat: timeSettings.timeFormat });
            const formattedDatetime = DateTime.fromJSDate(new Date(`${date} ${time}`))
              .plus({ minutes: timeSettings.timeOffset })
              .toFormat(`yyyy-MM-dd ${format}`);
            return formattedDatetime !== 'Invalid DateTime' ? formattedDatetime : date;
          },
        },
        timeSettings,
      ),
      TextColDef({
        field: 'description',
        headerName: 'Description',
        headerTooltip: getCalendarTooltip('description'),
        initialHide: true,
      }),
      TextColDef({
        field: 'description_extended',
        headerName: 'Extended Description',
        headerTooltip: getCalendarTooltip('description_extended'),
        initialHide: true,
      }),
    ],
    headerName: 'Options Activity',
  },
];

const economicsColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  FlagColDef({
    field: 'country',
    headerName: 'Country',
    initialPinned: true,
    initialWidth: 165,
  }),
  ...DateTimeColDef(
    {
      field: 'date',
      headerName: 'Date',
    },
    {
      field: 'time',
      headerName: 'Time',
    },
    timeSettings,
  ),
  TextColDef({
    field: 'event_name',
    headerName: 'Economic Event Name',
    initialWidth: 350,
  }),
  TextColDef({
    field: 'event_type',
    headerName: 'Type',
    initialWidth: 75,
    valueGetter: ({ data: { event_name } }: ValueGetterParams) => event_name?.match(/(fed|fomc)/i) && 'FED',
  }),
  PeriodColDef({
    field: 'event_period',
    headerName: 'Period',
    initialWidth: 75,
  }),
  NumberColDef({
    field: 'period_year',
    headerName: 'Year',
    initialWidth: 60,
    minWidth: 60,
    valueFormatter: ({ value }) => (isNotNil(value) ? value : NO_VALUE),
  }),
  NumberColDef({
    cellClassRules: {
      'u-downBg': (cell: CellClassParams) => cell.value < 0,
      'u-upBg': (cell: CellClassParams) => cell.value > 0,
    },
    field: 'actual',
    headerName: 'Actual',
  }),
  SetColDef({
    field: 'actual_t',
    headerName: 'Actual Type',
  }),
  NumberColDef({
    field: 'consensus',
    headerName: 'Consensus',
  }),
  SetColDef({
    field: 'consensus_t',
    headerName: 'Consensus Type',
  }),
  NumberColDef({
    field: 'prior',
    headerName: 'Prior',
  }),
  SetColDef({
    field: 'prior_t',
    headerName: 'Prior Type',
  }),
  ProgressColDef(
    {
      field: 'importance',
      headerName: 'Importance',
    },
    { high: 5, low: 1 },
  ),
];

const governmentTradesColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  TextColDef({
    field: 'security.name',
    headerName: 'Security Name',
  }),
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
    initialPinned: true,
    valueGetter: ({ data }) => data.security?.ticker,
  }),
  SetColDef(
    {
      field: 'security.type',
      headerName: 'Security Type',
      initialHide: true,
    },
    ['ETF', 'STOCK'],
  ),
  TextColDef({
    field: 'transaction_type',
    headerName: 'Transaction Type',
  }),
  NumberColDef({
    field: 'amount',
    headerName: 'Trade Amount',
    valueFormatter: ({ value }) => (value ? `${value}` : NO_VALUE),
  }),
  DateColDef(
    {
      field: 'report_date',
      headerName: 'Reported Date',
      initialHide: true,
    },
    timeSettings,
  ),
  DateColDef(
    {
      field: 'transaction_date',
      headerName: 'Transaction Date',
    },
    timeSettings,
  ),
  DateColDef(
    {
      field: 'notification_date',
      headerName: 'Notification Date',
      initialHide: true,
    },
    timeSettings,
  ),
  SetColDef(
    {
      field: 'chamber',
      headerName: 'Chamber',
    },
    ['House', 'Senate'],
  ),
  TextColDef({
    field: 'filer_info.member_name',
    headerName: 'Member Name',
  }),
  TextColDef({
    field: 'filer_info.status',
    headerName: 'Member Status',
    initialHide: true,
  }),
  SetColDef(
    {
      field: 'filer_info.state',
      headerName: 'Member State',
      initialHide: true,
    },
    [
      'AL',
      'AK',
      'AZ',
      'AR',
      'CA',
      'CO',
      'CT',
      'DE',
      'FL',
      'GA',
      'HI',
      'ID',
      'IL',
      'IN',
      'IA',
      'KS',
      'KY',
      'LA',
      'ME',
      'MD',
      'MA',
      'MI',
      'MN',
      'MS',
      'MO',
      'MT',
      'NE',
      'NV',
      'NH',
      'NJ',
      'NM',
      'NY',
      'NC',
      'ND',
      'OH',
      'OK',
      'OR',
      'PA',
      'RI',
      'SC',
      'SD',
      'TN',
      'TX',
      'UT',
      'VT',
      'VA',
      'WA',
      'WV',
      'WI',
      'WY',
    ],
  ),
  NumberColDef({
    field: 'filer_info.district',
    headerName: 'Member District',
    initialHide: true,
  }),
];

const guidanceColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    initialPinned: true,
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Date Announced',
        },
        {
          field: 'time',
          headerName: 'Time Announced',
        },
        timeSettings,
      ),
      TextColDef({
        field: 'name',
        headerName: 'Name',
      }),
      PeriodColDef({
        field: 'period',
        headerName: 'Period',
      }),
      NumberColDef({
        field: 'period_year',
        headerName: 'Period Year',
        valueFormatter: ({ value }) => (isNotNil(value) ? value : NO_VALUE),
        valueGetter: ({ colDef: { field = '' }, data }): string => {
          const value = data[field];
          return value || '-';
        },
      }),
      BooleanColDef(
        {
          field: 'prelim',
          headerName: 'Preliminary Guidance',
          valueGetter: data => (data.data.prelim === 'Y' ? 'Preliminary' : null),
        },
        '',
        'Preliminary',
      ),
      NumberColDef({
        field: 'eps_guidance_min',
        headerName: 'Min EPS Guidance',
      }),
      NumberColDef({
        field: 'eps_guidance_max',
        headerName: 'Max EPS Guidance',
      }),
      NumberColDef({
        field: 'eps_guidance_est',
        headerName: 'Estimated EPS Consensus',
      }),
      NumberColDef({
        field: 'revenue_guidance_min',
        headerName: 'Min Revenue Guidance',
      }),
      NumberColDef({
        field: 'revenue_guidance_max',
        headerName: 'Max Revenue Guidance',
      }),
      NumberColDef({
        field: 'revenue_guidance_est',
        headerName: 'Revenue Estimate',
      }),
      NumberColDef({
        field: 'refinitiv_gps',
        headerName: 'Estimated GPS',
      }),
      NumberColDef({
        field: 'refinitiv_cps',
        headerName: 'Estimated CPS',
      }),
      NumberColDef({
        field: 'refinitiv_grm',
        headerName: 'Estimated GRM',
      }),
      NumberColDef({
        field: 'refinitiv_ffo',
        headerName: 'Estimated FFO',
      }),
    ],
    headerName: 'Guidance',
  },
];

const IposColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    initialPinned: true,
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Date',
        },
        {
          field: 'time',
          headerName: 'Time',
        },
        timeSettings,
      ),
      TextColDef({
        field: 'exchange',
        headerName: 'Exchange',
        initialHide: false,
      }),
      TextColDef({
        field: 'name',
        headerName: 'Name',
      }),
      DateColDef(
        {
          field: 'pricing_date',
          headerName: 'Date of IPO Pricing',
        },
        timeSettings,
      ),
      NumberColDef({
        field: 'price_min',
        headerName: 'Price Min',
      }),
      NumberColDef({
        field: 'price_max',
        headerName: 'Price Max',
      }),
      TextColDef({
        field: 'ipo_type',
        headerName: 'IPO type',
        initialHide: true,
      }),
      BooleanColDef({
        field: 'spac_converted_to_target',
        headerName: 'Has a SPAC converted to its target ticker?',
        initialHide: true,
      }),
      NumberColDef({
        field: 'price_public_offering',
        headerName: 'Public offering price (POP)',
        initialHide: true,
      }),
      TextColDef({
        field: 'insider_lockup_days',
        headerName: 'Insider Lockup Days',
        initialHide: true,
      }),
      DateColDef(
        {
          field: 'insider_lockup_date',
          headerName: 'Insider Lockup Date',
        },
        timeSettings,
      ),
      NumberColDef({
        field: 'offering_value',
        headerName: 'Offering Value',
      }),
      NumberColDef({
        field: 'offering_shares',
        headerName: 'Offering Shares',
      }),
      NumberColDef({
        field: 'shares_outstanding',
        headerName: 'Outstanding Shares',
        initialHide: true,
      }),
      NumberColDef({
        field: 'ord_shares_out_after_offer',
        headerName: 'Total ordinary shares out after the offering',
        initialHide: true,
      }),
      NumberColDef({
        field: 'market_cap_at_offer',
        headerName: 'Total market cap at the moment of the offer',
        initialHide: true,
      }),
      NumberColDef({
        field: 'offering_shares_ord_adr',
        headerName: 'For ADRs, the total amount of ordinary shares that the ADRs represent	',
        initialHide: true,
      }),
      NumberColDef({
        field: 'shares_from_selling_holders',
        headerName: 'Shares Available from Selling Holders',
        initialHide: true,
      }),
      TextColDef({
        field: 'lead_underwriters',
        headerName: 'Lead Underwriter(s)',
      }),
      TextColDef({
        field: 'other_underwriters',
        headerName: 'Other Underwriter(s)',
      }),
      TextColDef({
        field: 'deal_status',
        headerName: 'Deal Status',
      }),
      TextColDef({
        field: 'sec_filing_url',
        headerName: 'F1/S1/other link information is pulled from',
        initialHide: true,
      }),
      NumberColDef({
        field: 'last_yr_revenue',
        headerName: "Last year's revenue (in USD)",
        initialHide: true,
      }),
      NumberColDef({
        field: 'last_yr_revenue_year',
        headerName: 'The year the "last year\'s revenue" refers to',
        initialHide: true,
      }),
      NumberColDef({
        field: 'last_yr_income',
        headerName: "Last year's income (in USD)",
        initialHide: true,
      }),
      NumberColDef({
        field: 'last_yr_income_year',
        headerName: 'The year the "last year\'s income" refers to',
        initialHide: true,
      }),
      TextColDef({
        field: 'description',
        headerName: 'Company description, including description of target for SPAC',
        initialHide: true,
      }),
      NumberColDef({
        field: 'sic',
        headerName: 'SIC code',
        initialHide: true,
      }),
      TextColDef({
        field: 'state_location',
        headerName: 'State location code',
        initialHide: true,
      }),
      TextColDef({
        field: 'sec_accession_number',
        headerName: 'SEC Accession Number',
        initialHide: true,
      }),
      NumberColDef({
        field: 'underwriter_quiet_expiration_days',
        headerName: 'Underwrite Quite Period',
        initialHide: true,
      }),
      DateColDef(
        {
          field: 'underwriter_quiet_expiration_date',
          headerName: 'Underwrite Quiet Period Date',
          initialHide: true,
        },
        timeSettings,
      ),
    ],
    headerName: 'IPOs',
  },
];

const mergersAndAcquisitionsColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'acquirer_ticker',
    headerName: 'Acquirer Symbol',
    initialPinned: true,
  }),
  {
    children: [
      DateColDef(
        {
          field: 'date',
          headerName: 'Announced',
        },
        timeSettings,
      ),
      DateColDef(
        {
          field: 'date_expected',
          headerName: 'Expected',
          initialSort: 'desc',
        },
        timeSettings,
      ),
      DateColDef(
        {
          field: 'date_completed',
          headerName: 'Completed',
        },
        timeSettings,
      ),

      TextColDef({
        field: 'acquirer_exchange',
        headerName: 'Acquirer Exchange',
      }),
      TextColDef({
        field: 'acquirer_name',
        headerName: 'Acquirer Name',
      }),
      SymbolColDef({
        field: 'target_ticker',
        headerName: 'Target Symbol',
      }),
      TextColDef({
        field: 'target_exchange',
        headerName: 'Target Exchange',
      }),
      TextColDef({
        field: 'target_name',
        headerName: 'Target Name',
      }),
      TextColDef({
        field: 'currency',
        headerName: 'Currency',
      }),
      TextColDef({
        field: 'deal_type',
        headerName: 'Type',
      }),
      NumberColDef({
        field: 'deal_size',
        headerName: 'Size',
      }),
      TextColDef({
        field: 'deal_payment_type',
        headerName: 'Payment Type',
      }),
      TextColDef({
        field: 'deal_status',
        headerName: 'Status',
      }),
      TextColDef({
        field: 'deal_terms_extra',
        headerName: 'Terms',
        initialHide: true,
      }),
      ProgressColDef(
        {
          field: 'importance',
          headerName: 'Importance',
          initialHide: true,
        },
        { high: 5, low: 1 },
      ),
      TextColDef({
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
        initialHide: true,
      }),
    ],
    headerName: 'Mergers & Acquisitions',
  },
];

const offeringsColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
    initialPinned: true,
  }),
  {
    children: [
      DateColDef(
        {
          field: 'date',
          headerName: 'Date',
        },
        timeSettings,
      ),

      TextColDef({
        field: 'name',
        headerName: 'Name',
      }),
      NumberColDef({
        field: 'dollar_shares',
        headerName: 'Share Value',
      }),
      NumberColDef({
        field: 'number_shares',
        headerName: 'Share Quantity',
      }),
      NumberColDef({
        field: 'price',
        headerName: 'Price',
      }),
      NumberColDef({
        field: 'proceeds',
        headerName: 'Proceeds',
      }),

      BooleanColDef(
        {
          field: 'shelf',
          headerName: 'Shelf',
        },
        'false',
        'true',
      ),
    ],
    headerName: 'Offerings',
  },
];

const ratingsColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
    initialPinned: true,
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Date',
        },
        {
          field: 'time',
          headerName: 'Time',
          initialHide: true,
        },
        timeSettings,
      ),

      TextColDef({
        field: 'name',
        headerName: 'Name',
        initialHide: true,
      }),
      TextColDef({
        cellClassRules: {
          'u-downBg': (cell: CellClassParams) => cell.value.search(/downgrad/i) >= 0,
          'u-upBg': (cell: CellClassParams) => cell.value.search(/upgrad/i) >= 0,
        },
        field: 'action_company',
        headerName: 'Action on Security',
      }),
      TextColDef({
        cellClassRules: {
          'u-downBg': (cell: CellClassParams) => ['Sell', 'Underperform', 'Underweight'].includes(cell.value),
          'u-upBg': (cell: CellClassParams) =>
            ['Buy', 'Conviction Buy', 'Market Outperform', 'Outperform', 'Overweight', 'Strong Buy'].includes(
              cell.value,
            ),
        },
        field: 'rating_current',
        headerName: 'Rating',
      }),
      NumberColDef({
        field: 'pt_current',
        headerName: 'Price Target',
      }),
      TextColDef({
        field: 'action_pt',
        headerName: 'Action Price Target',
      }),
      TextColDef({
        field: 'analyst',
        headerName: 'Analyst Firm',
      }),
      TextColDef({
        field: 'analyst_name',
        headerName: 'Analyst Name',
        initialHide: true,
      }),
      NumberColDef({
        field: 'pt_prior',
        headerName: 'Prior Price Target',
      }),
      TextColDef({
        field: 'rating_prior',
        headerName: 'Prior Rating',
      }),
      PercentNumberColDef(
        {
          field: 'pt_pct_change',
          headerName: 'Price Target % Change',
        },
        1,
      ),
      NumberColDef({
        field: 'ratings_accuracy.smart_score',
        headerName: 'Smart Score',
        initialSort: 'desc',
      }),
      PercentNumberColDef(
        {
          field: 'ratings_accuracy.overall_success_rate',
          headerName: 'Overall Success Rate',
        },
        1,
      ),
      NumberColDef({
        field: 'ratings_accuracy.total_ratings',
        headerName: 'Total Ratings',
      }),
      ChangePercentColDef({
        field: 'upside_downside',
        headerName: 'Upside/Downside',
        sortable: true,
        valueGetter: ({ context, data: { pt_current, ticker = '' } }: ValueGetterParams) => {
          const { lastTradePrice } =
            context.session.getManager(QuotesManager).getCachedQuote(ticker) ??
            context.session.getManager(QuotesManager).getDetailedQuotesCached([ticker])?.[ticker] ??
            {};
          return lastTradePrice && pt_current
            ? (((pt_current - lastTradePrice) / lastTradePrice) * 100).toFixed(2)
            : '';
        },
      }),
    ],
    headerName: 'Ratings',
  },
];

// const retailColumnDefs = ( timeSettings: DateColDefTimeSettings): ColDef[] => [
//   {
//     comparator: comparators.dateTimeComparator(),
//     field: 'date',
//     filter: 'agDateColumnFilter',
//     filterParams: dateComparator,
//     headerName: 'Date',
//     valueGetter: valueGetter.dateHOF('date', 'time', settings.timeOffset),
//   },
//   {
//     ...getTimeColumnParams('time', settings),
//     headerName: 'Time',
//   },
//   {
//     ...columnDefs(session.getManager(QuotesManager)).symbol,
//     field: 'ticker',
//     initialPinned: false,
//   },
//   {
//     cellStyle: { 'text-align': 'left' },
//     field: 'name',
//     filter: 'agTextColumnFilter',
//     headerName: 'Name',
//   },
//   {
//     cellRenderer: cellRendererFramework.marketPeriod,
//     cellStyle: { 'text-align': 'left' },
//     field: 'period',
//     filter: 'agTextColumnFilter',
//     headerName: 'Period',
//   },
//
//     field: 'period_year',
//     filter: 'agNumberColumnFilter',
//     headerName: 'Period Year',
//     valueGetter: valueGetter.yearFormatter,
//   },
//   {
//     cellRenderer: cellRenderer.roundPercent
//     field: 'sss',
//     filter: NUMBER_SHORTHAND_FILTER,
//     headerName: 'Same Store Sales',
//   },
//
// ];

const secColumnDefs = (timeSettings: DateColDefTimeSettings): (ColDef | ColGroupDef)[] => [
  SymbolColDef({
    field: 'ticker',
    headerName: 'Symbol',
    initialPinned: true,
  }),
  {
    children: [
      ...DateTimeColDef(
        {
          field: 'date',
          headerName: 'Date Accepted',
        },
        {
          field: 'time',
          headerName: 'Time Accepted',
        },
        timeSettings,
      ),
      DateColDef(
        {
          field: 'date_filed',
          headerName: 'Date Filed',
        },
        timeSettings,
      ),
      DateColDef(
        {
          cellStyle: { 'text-align': 'right' },
          field: 'date_filing_changed',
          headerName: 'Date Changed',
        },
        timeSettings,
      ),

      TextColDef({
        field: 'form_type',
        headerName: 'Form Type',
      }),
      BooleanColDef(
        {
          field: 'amendment',
          headerName: 'Amended',
        },
        '',
        'Amended',
        false,
      ),
      TextColDef({
        field: 'issuer_name',
        headerName: 'Issuer Name',
      }),
      LinkColDef({
        field: 'issuer_cik',
        filter: false,
        headerName: 'Issuer CIK',
        valueGetter: ({ data }) =>
          data.issuer_cik
            ? `https://www.sec.gov/cgi-bin/browse-edgar?CIK=${data.issuer_cik}&owner=exclude&action=getcompany&Find=Search`
            : '',
      }),
      LinkColDef({
        field: 'reporter_cik',
        filter: false,
        headerName: 'Reporter CIK',
        valueGetter: ({ data }) =>
          data.reporter_cik
            ? `https://www.sec.gov/cgi-bin/browse-edgar?CIK=${data.reporter_cik}&owner=exclude&action=getcompany&Find=Search`
            : '',
      }),
      LinkColDef({
        field: 'owner_cik',
        filter: false,
        headerName: 'Owner CIK',
        valueGetter: ({ data }) =>
          data.owner_cik
            ? `https://www.sec.gov/cgi-bin/browse-edgar?CIK=${data.owner_cik}&owner=exclude&action=getcompany&Find=Search`
            : '',
      }),
      TextColDef({
        field: 'accession_number',
        headerName: 'Accession Number',
      }),
    ],
    headerName: 'SEC Filings',
  },
];

export const shortInterestColumnDefs = (timeSettings: DateColDefTimeSettings): ColDef[] => [
  SymbolColDef({
    field: 'symbol',
    initialPinned: true,
  }),
  DateColDef(
    {
      field: 'settlementDate',
      headerName: 'Settlement Date',
      minWidth: 100,
    },
    timeSettings,
  ),
  DateColDef(
    {
      field: 'exchangeReceiptDate',
      headerName: 'Receipt Date',
      minWidth: 100,
    },
    timeSettings,
  ),
  DateColDef(
    {
      field: 'recordDate',
      headerName: 'Effective Date',
      initialSort: 'desc' as ColDef['initialSort'],
      minWidth: 100,
    },
    timeSettings,
  ),
  PercentNumberColDef(
    {
      field: 'shortPercentOfFloat',
      headerName: '% of Float Shorted',
      headerTooltip: '',
    },
    1,
  ),
  NumberColDef({
    field: 'sharesFloat',
    headerName: 'Float',
  }),
  NumberColDef({
    field: 'totalShortInterest',
    headerName: 'Short Interest',
  }),
  NumberColDef({
    field: 'averageDailyVolume',
    headerName: 'Average Volume (10 day)',
  }),
  NumberColDef({
    field: 'daysToCover',
    headerName: 'Days to Cover',
    minWidth: 100,
  }),
];

export const splitsColumnDefs = (timeSettings: DateColDefTimeSettings): ColDef[] => [
  DateColDef(
    {
      field: 'date_ex',
      headerName: 'Ex-Date',
      headerTooltip: 'A purchase on that date (or after) will be ex (outside, without right to) the dividend',
    },
    timeSettings,
  ),
  SymbolColDef({
    field: 'ticker',
    initialPinned: true,
  }),
  TextColDef({
    field: 'exchange',
    headerName: 'Exchange',
  }),
  TextColDef({
    field: 'name',

    headerName: 'Name',
  }),
  TextColDef({
    field: 'split_type',
    headerName: 'Split Type',
  }),
  TextColDef({
    field: 'ratio',
    headerName: 'Split Ratio',
  }),
  DateColDef(
    {
      field: 'date_announced',
      headerName: 'Date Announced',
    },
    timeSettings,
  ),
  DateColDef(
    {
      field: 'date_recorded',
      headerName: 'Recorded Date',
    },
    timeSettings,
  ),
  DateColDef(
    {
      field: 'date_distribution',
      headerName: 'Distribution Date',
    },
    timeSettings,
  ),
];

export const squawkColumnDefs = (timeSettings: DateColDefTimeSettings): ColDef[] => [
  DateColDef(
    {
      field: 'timestamp',
      headerName: 'Date Time',
      initialSort: 'desc',
      valueGetter: ({ data }: { data: Squawk }) => {
        const format = getTimeDisplayFormat({ timeFormat: timeSettings.timeFormat });
        const formattedDatetime = DateTime.fromMillis(data.timestamp * 1000)
          .plus({ minutes: timeSettings.timeOffset })
          .toFormat(`yyyy-MM-dd ${format}`);
        return formattedDatetime !== 'Invalid DateTime' ? formattedDatetime : data.timestamp;
      },
    },
    timeSettings,
  ),
  SymbolColDef({
    field: 'securities',
    headerName: 'Symbol',
    valueGetter: ({ data }: { data: Squawk }) => data.securities?.map(se => se.symbol),
  }),
  TextColDef({
    autoHeight: true,

    field: 'text',
    headerName: 'Squawk',
    initialWidth: 600,
    wrapText: true,
  }),
  TextColDef({
    field: 'channel_name',
    headerName: 'Channel Name',
  }),
];

const calendarDefaultColDef = {
  filter: undefined,
  minWidth: 85,
  resizable: true,
  sortable: true,
};

type CalendarDefinitions = { [calendarType: string]: CalendarDefinition };

export const calendarDefinitions: CalendarDefinitions = {
  conference: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'left' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Conference Calls',
  },
  dividends: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Dividends',
  },
  earnings: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Earnings',
  },
  economics: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'left' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Economics',
  },
  fda: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'left' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'FDA',
  },
  governmentTrades: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'left' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Government Trades',
  },
  guidance: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Guidance',
  },
  ipos: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 14,
        dateStartDaysOffset: -14,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'IPOs',
  },
  ma: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 30,
        dateStartDaysOffset: -30,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Mergers & Acquisitions',
  },
  offerings: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Secondary Offerings',
  },
  optionsActivity: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'left' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Unusual Options Activity',
  },
  ratings: {
    defaultColDef: {
      ...calendarDefaultColDef,
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Analyst Ratings',
  },
  sec: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'left' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 0,
        dateStartDaysOffset: 0,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'SEC Filings',
  },
  shortInterest: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 14,
        dateStartDaysOffset: -14,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Short Interest',
  },
  splits: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 14,
        dateStartDaysOffset: -14,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Splits',
  },
  squawk: {
    defaultColDef: {
      ...calendarDefaultColDef,
      cellStyle: { 'text-align': 'right' },
    },
    defaultDates: {
      nothingSearched: {
        dateEndDaysOffset: 14,
        dateStartDaysOffset: -14,
      },
      somethingSearched: {
        dateEndDaysOffset: 90,
        dateStartDaysOffset: null,
      },
    },
    name: 'Squawk',
  },
};

const getterDefs = {
  conference: { getter: conferenceColumnDefs, tutorialClass: 'Calendar_Conference' },
  dividends: { getter: dividendsColumnDefs, tutorialClass: 'Calendar_Dividends' },
  earnings: { getter: earningsColumnDefs, tutorialClass: 'Calendar_Earnings' },
  economics: { getter: economicsColumnDefs, tutorialClass: 'Calendar_Economics' },
  fda: { getter: fdaColumnDefs, tutorialClass: 'Calendar_FDA' },
  governmentTrades: { getter: governmentTradesColumnDefs, tutorialClass: 'Government_Trades' },
  guidance: { getter: guidanceColumnDefs, tutorialClass: 'Calendar_Guidance' },
  ipos: { getter: IposColumnDefs, tutorialClass: 'Calendar_IPOS' },
  ma: { getter: mergersAndAcquisitionsColumnDefs, tutorialClass: 'Calendar_MA' },
  offerings: { getter: offeringsColumnDefs, tutorialClass: 'Calendar_Offerings' },
  optionsActivity: { getter: optionsActivityDefs, tutorialClass: 'Calendar_UOA' },
  ratings: { getter: ratingsColumnDefs, tutorialClass: 'Calendar_Ratings' },
  sec: { getter: secColumnDefs, tutorialClass: 'Calendar_SEC' },
  shortInterest: { getter: shortInterestColumnDefs, tutorialClass: 'Calendar_Short_Interest' },
  splits: { getter: splitsColumnDefs, tutorialClass: 'Calendar_Splits' },
  squawk: { getter: squawkColumnDefs, tutorialClass: 'Calendar_Squawk' },
};

export const getCalendarColDefs = (calendarType: CalendarType, timeSettings: DateColDefTimeSettings) => {
  const { getter, tutorialClass } = getterDefs[calendarType];
  return addTutorialClasses(getter(timeSettings), tutorialClass);
};

export const getTickerFieldFromCalendarType = (calendarType: CalendarType) => (data: any) => {
  switch (calendarType) {
    case 'conference':
    case 'dividends':
    case 'earnings':
    case 'governmentTrades':
    case 'guidance':
    case 'ipos':
    case 'offerings':
    case 'optionsActivity':
    case 'ratings':
    case 'sec':
    case 'splits':
    case 'squawk':
      return data['ticker'];
    case 'ma':
      return data['acquirer_ticker'];
    case 'fda': {
      const tickers =
        data['ticker'] ||
        data['companies']?.[0]?.securities?.reduce(
          (tickers: StockSymbol[], security: { symbol: StockSymbol }) =>
            security?.symbol ? [...tickers, security.symbol] : tickers,
          [],
        );
      return Array.isArray(tickers) ? tickers[0] : tickers;
    }
    case 'shortInterest':
      return data['symbol'];
    case 'economics':
    default:
      return undefined;
  }
};
