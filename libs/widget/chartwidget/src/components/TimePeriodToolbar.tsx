'use client';
import React, { useCallback } from 'react';
import { Button, Dropdown, Icon, SelectMenu } from '@benzinga/core-ui';
import { faChevronDown } from '@fortawesome/pro-regular-svg-icons/faChevronDown';
import styled from '@benzinga/themetron';

const time_periods = ['1Y', '1D', '5D', '1M', '3M', '6M', '1Y', '5Y', '10Y'];

const time_period_options = time_periods.map(time_period => {
  return { key: time_period, name: time_period };
});

const intervals = ['1d', '1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '3d', '1w', '1mo'];

const interval_options = intervals.map(interval => {
  return { key: interval, name: interval };
});

interface TimePeriodToolbarProps {
  onIntervalChange: (value: string) => void;
  onTimeframeChange: (value: string) => void;
  interval: string;
  timeframe: string;
}

const TimePeriodToolbar = ({ interval, onIntervalChange, onTimeframeChange, timeframe }: TimePeriodToolbarProps) => {
  const [intervalOpen, setIntervalOpen] = React.useState(false);
  const [timeframeOpen, setTimeframeOpen] = React.useState(false);

  const handleIntervalToggle = useCallback(
    (toggle: boolean) => {
      setIntervalOpen(toggle);
    },
    [setIntervalOpen],
  );
  const handleTimeframeToggle = useCallback(
    (toggle: boolean) => {
      setTimeframeOpen(toggle);
    },
    [setTimeframeOpen],
  );

  const handleIntervalChange = useCallback(
    (value: string) => {
      onIntervalChange && onIntervalChange(value);
    },
    [onIntervalChange],
  );

  const handleTimeframeChange = useCallback(
    (value: string) => {
      onTimeframeChange && onTimeframeChange(value);
    },
    [onTimeframeChange],
  );

  return (
    <FinancialAssetPositionSelectWrapper className="financial-asset-position-select-wrapper">
      <div style={{ display: 'flex', marginTop: '15px', minWidth: '160px' }}>
        <div style={{ marginLeft: '10px' }}>
          <Dropdown
            distance={0}
            onClose={undefined}
            onToggle={handleTimeframeToggle}
            open={timeframeOpen}
            target={
              <div className="dropdown-items-container">
                {timeframe ? (
                  <Button size="sm" variant={'flat'}>
                    <span style={{ marginRight: '0.25rem' }}>Timeframe: {timeframe}</span>
                    <Icon className="chevron-down" icon={faChevronDown} />
                  </Button>
                ) : (
                  <Button size="sm" variant={'flat'}>
                    <span style={{ marginRight: '0.25rem' }}>Timeframe</span>
                    <Icon className="chevron-down" icon={faChevronDown} />
                  </Button>
                )}
              </div>
            }
            trigger={'hover'}
          >
            <SelectMenu
              onChange={handleTimeframeChange}
              open={true}
              options={time_period_options}
              selected={timeframe}
            />
          </Dropdown>
        </div>
        <div style={{ marginLeft: '10px' }}>
          <Dropdown
            distance={0}
            onClose={undefined}
            onToggle={handleIntervalToggle}
            open={intervalOpen}
            target={
              <div className="dropdown-items-container">
                {interval ? (
                  <Button size="sm" variant={'flat'}>
                    <span style={{ marginRight: '0.25rem' }}>Interval: {interval}</span>
                    <Icon className="chevron-down" icon={faChevronDown} />
                  </Button>
                ) : (
                  <Button size="sm" variant={'flat'}>
                    <span style={{ marginRight: '0.25rem' }}>Interval</span>
                    <Icon className="chevron-down" icon={faChevronDown} />
                  </Button>
                )}
              </div>
            }
            trigger={'hover'}
          >
            <SelectMenu onChange={handleIntervalChange} open={true} options={interval_options} selected={interval} />
          </Dropdown>
        </div>
      </div>
    </FinancialAssetPositionSelectWrapper>
  );
};

export default TimePeriodToolbar;

const FinancialAssetPositionSelectWrapper = styled.div`
  &.financial-asset-position-select-wrapper {
    .select-menu-list {
      font-weight: ${({ theme }) => theme.fontWeight.bold};
    }

    .select-menu-list-item {
      padding: 0.25rem 1rem;
    }

    .dropdown-items-container {
      outline: none;
      cursor: pointer;

      button {
        height: 28px;
      }

      &:focus {
        outline: none;
      }

      /* group */

      display: inline-flex;
      position: relative;

      .financial-asset-position-wrapper {
        height: 1.75rem;
        display: flex;
        align-items: center;
        border-radius: 0.25rem;
        padding: 0.125rem;
      }

      .chevron-down {
        font-size: 0.75rem;
        line-height: 1rem;
      }
    }
  }
`;
