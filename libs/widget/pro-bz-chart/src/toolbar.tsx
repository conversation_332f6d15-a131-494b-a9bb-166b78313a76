'use client';
import React from 'react';
import {
  TagSearch,
  TagSearchLinked,
  useClearTool,
  useClipboardTool,
  useDropdownTool,
  useLinkingSearchModule,
  useScannerSearchModule,
  useSymbolSearchModule,
  useWatchlistSearchModule,
} from '@benzinga/pro-ui';
import { ChartModuleItem } from './entity';
import { TC } from '@benzinga/themetron';
import { WidgetToolbarContext, useWidgetParameters } from '@benzinga/widget-tools';
import { BZChartWidgetManifest } from './widget';

export const ChartToolbar: React.FC = () => {
  const widgetToolbar = React.useContext(WidgetToolbarContext);
  const widgetParameters = useWidgetParameters(BZChartWidgetManifest);
  const setParameters = widgetParameters.setParameters;

  const symbolModule = useSymbolSearchModule();
  const watchlistModule = useWatchlistSearchModule();
  const linkingModule = useLinkingSearchModule();
  const scannerModule = useScannerSearchModule();
  const modules = React.useMemo(
    () => [symbolModule, watchlistModule, scannerModule, linkingModule],
    [symbolModule, watchlistModule, scannerModule, linkingModule],
  );

  const onTagsUpdated = React.useCallback(
    (source: ChartModuleItem[]): void => {
      setParameters(params => ({ ...params, source }));
    },
    [setParameters],
  );

  const onSingleTagsUpdated = React.useCallback(
    (source: ChartModuleItem): void => {
      setParameters(params => ({ ...params, source: [source] }));
    },
    [setParameters],
  );

  React.useEffect(() => {
    const defineControls = widgetToolbar.defineControls;
    defineControls({
      key: 'search',
      toolbarNode: (
        <TagSearch
          modules={modules}
          onTagsUpdated={onTagsUpdated as any}
          placeholder="Search by $Symbol or Security Name..."
          tags={widgetParameters.parameters.source}
          thinUI={true}
        />
      ),
    });
  }, [modules, onTagsUpdated, widgetParameters.parameters.source, widgetToolbar.defineControls]);

  const clearTool = useClearTool(
    React.useCallback(() => {
      onTagsUpdated([]);
    }, [onTagsUpdated]),
  );
  const dropDownTool = useDropdownTool();
  const clipboardTool = useClipboardTool();
  const tools = React.useMemo(() => [dropDownTool, clearTool, clipboardTool], [dropDownTool, clearTool, clipboardTool]);

  const singleGrid = React.useMemo(() => {
    const colCount = widgetParameters.parameters.gridLayout.colCount;
    const rowCount = widgetParameters.parameters.gridLayout.rowCount;
    const col = typeof colCount === 'number' ? colCount : typeof colCount === 'string' ? parseInt(colCount) : 1;
    const row = typeof rowCount === 'number' ? rowCount : typeof rowCount === 'string' ? parseInt(rowCount) : 1;
    return (col === 1 && row === 1) || widgetParameters.parameters.gridSettings.syncTicker;
  }, [
    widgetParameters.parameters.gridLayout.colCount,
    widgetParameters.parameters.gridLayout.rowCount,
    widgetParameters.parameters.gridSettings.syncTicker,
  ]);

  return (
    <TC.Row>
      {singleGrid ? (
        <TagSearchLinked
          modules={modules}
          onTagUpdated={onSingleTagsUpdated as any}
          placeholder="Add $Symbol, +Link or Security Name... To Chart"
          tag={widgetParameters.parameters.source[0]}
          tools={tools}
        />
      ) : (
        <TagSearch
          modules={modules}
          onTagsUpdated={onTagsUpdated as any}
          placeholder="Add $Symbol, +Link or Security Name... To Chart"
          tags={widgetParameters.parameters.source}
          tools={tools}
        />
      )}
    </TC.Row>
  );
};
