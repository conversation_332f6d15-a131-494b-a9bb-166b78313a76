import { WidgetManifest } from '@benzinga/widget-tools';
import { WnstnChatAllIterations, WnstnChatIteration } from './entities';
import { WnstnChatMigrator } from './migrator';
import React from 'react';
import { SessionContext } from '@benzinga/session-context';
import { PermissionOverlay } from '@benzinga/pro-ui';
import { UserManager } from '@benzinga/user-manager';
import { message } from 'antd';
import { TrackingManager } from '@benzinga/tracking-manager';
import { BenzingaAI } from '@benzinga/icons';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import styled from 'styled-components';

declare global {
  interface Window {
    chatInitParams: { proxy: string; userId: string };
  }
}

const WnstnChatWidget = () => {
  const session = React.useContext(SessionContext);
  const isUserLoggedIn = useIsUserLoggedIn();
  const [messageApi, contextHolder] = message.useMessage();

  const uuid = React.useMemo(
    () => ({
      proxy: 'https://wnstn-api.benzinga.com/pro',
      userId:
        session.getManager(UserManager).getUser()?.accessType !== 'anonymous'
          ? session.getManager(UserManager).getUser()?.id.toString() ?? ''
          : 'annoymous',
    }),
    [session],
  );

  React.useEffect(() => {
    session.getManager(TrackingManager).trackWidgetEvent('click', 'wnstn_widget', {
      button_value: 'wnstn_widget_open',
    });
    messageApi.info('Benzinga AI is in partnership with the WNSTN team for free. Availability subject to change.');
    return () => {
      session.getManager(TrackingManager).trackWidgetEvent('click', 'wnstn_widget', {
        button_value: 'wnstn_widget_close',
      });
    };
  }, [messageApi, session]);

  React.useEffect(() => {
    window.chatInitParams = uuid;
    let script = document.getElementById('wnstn-chat-script') as HTMLScriptElement;

    if (!script) {
      script = document.createElement('script');
      script.src = 'https://staticfiles.wnstn.ai/jsfiles/createWidget.js';
      script.async = true;
      script.id = 'wnstn-chat-script';
      document.body.appendChild(script);
    }

    return () => {
      if (script) {
        document.body.removeChild(script);
      }
    };
  }, [uuid]);

  if (!isUserLoggedIn) {
    return <PermissionOverlay text={'Please login to access Benzinga AI'}> </PermissionOverlay>;
  }

  return (
    <>
      {contextHolder}
      <div className="my-chat-container" style={{ height: '100%', width: '100%' }}>
        <div className="my-chat-container-container__widget" style={{ height: '100%', width: '100%' }} />
      </div>
    </>
  );
};

export const WnstnChatManifest: WidgetManifest<'wnstn-chat', WnstnChatIteration, WnstnChatAllIterations> = {
  WidgetRender: WnstnChatWidget,
  defaultGlobalParameters: {},
  defaultWidgetParameters: {},
  description: 'Beniznga AI',
  icon: () => <StyledWnstnLogo />,
  id: 'wnstn-chat',
  menuItem: true,
  migrator: WnstnChatMigrator,
  name: 'Benzinga AI',
  permission: { action: 'bzpro/wnstn-chat', resource: 'wnstn-chat' },
  state: 'new',
  version: 1,
};
const StyledWnstnLogo = styled(BenzingaAI)`
  width: 20px !important;
  height: 15px !important;
  margin: 5px !important;
  rect {
    fill: ${props => props.theme.colors.foregroundMuted};
  }
  path {
    background: ${props => props.theme.colors.foreground};
  }
`;
