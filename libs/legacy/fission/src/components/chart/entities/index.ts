import { Time } from 'lightweight-charts';
import moment from 'moment';

// it is allows to display the data more neatly
// was being picked up visually.

/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
export enum ChartRangeSift {
  '1d' = 1,
  '5d' = 2,
  '1m' = 2,
  '3m' = 2,
  '6m' = 2,
  'YTD' = 2,
  '1y' = 2,
  '2y' = 2,
  '5y' = 5,
  '10y' = 10,
}
/* eslint-enable @typescript-eslint/no-duplicate-enum-values */

/* eslint-disable typescript-sort-keys/string-enum */
export enum ChartRange {
  '1d' = '1d',
  '5d' = '5d',
  '1m' = '1m',
  '3m' = '3m',
  '6m' = '6m',
  'YTD' = 'YTD',
  '1y' = '1y',
  '2y' = '2y',
  '5y' = '5y',
  '10y' = '10y',
}
/* eslint-enable typescript-sort-keys/string-enum */

export enum ChartType {
  'candlestick' = 'candlestick',
  'line' = 'line',
}

export const availableChartTypes: ChartType[] = [ChartType.line, ChartType.candlestick];

interface ChartConfigItem {
  from: string;
  interval: string;
  refreshRate: number;
}

export interface ChartConfig {
  [period: string]: ChartConfigItem;
}

export const MAX_DATE_FROM = '1970-01-15';
export const returnYearFromOrNaN = (periodFrom: ChartRange | typeof MAX_DATE_FROM) => moment(periodFrom).year();
export const chartConfig: ChartConfig = {
  // 365 = (365 / 5) * 5
  '10y': { from: '10y', interval: '10d', refreshRate: 360000 },

  /* eslint-disable sort-keys */
  '1d': { from: '1d', interval: '2m', refreshRate: 40000 },

  // 360 = (24 * (60 / 20)) * 5
  '1m': { from: '1m', interval: '2h', refreshRate: 90000 },

  // 365
  '1y': { from: '1y', interval: '1d', refreshRate: 360000 },

  // 365
  '2y': { from: '2y', interval: '2d', refreshRate: 360000 },

  // 372 = (24 /2) * 31
  '3m': { from: '3m', interval: '6h', refreshRate: 360000 },

  // 720 = (60 / 2) * 24
  '5d': { from: '5d', interval: '20m', refreshRate: 60000 },

  // 365 = (365 / 2) * 2
  '5y': { from: '5y', interval: '5d', refreshRate: 360000 },

  // 372 = (24 / 6) * (31 * 3)
  '6m': { from: '6m', interval: '12h', refreshRate: 360000 },
  // 372 = (24 / 12) * (31 * 3)
  YTD: { from: 'YTD', interval: '1d', refreshRate: 360000 }, // 1274 = (52 / 2) * 49
  /* eslint-enable sort-keys */
};

export const availableChartRanges: ChartRange[] = [
  ChartRange['1d'],
  ChartRange['5d'],
  ChartRange['1m'],
  ChartRange['3m'],
  ChartRange['6m'],
  ChartRange.YTD,
  ChartRange['1y'],
  ChartRange['2y'],
  ChartRange['5y'],
  ChartRange['10y'],
];

export interface ChartData {
  close: number;
  dateTime: string;
  high: number;
  low: number;
  open: number;
  time: Time;
  volume: number;
}

export enum ChartView {
  candlestick = 'addCandlestickSeries',
  line = 'addAreaSeries',
}
