import React, { useCallback, useEffect, useState } from 'react';
import {
  FlatList,
  Image,
  LayoutAnimation,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  UIManager,
  View,
} from 'react-native';
import PremiumCard, { PremiumCardHeader } from './PremiumCard';
import { useTheme } from '../../theme/themecontext';
import { StackNavigationProp } from '@react-navigation/stack';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { AppNavigationStackParamList } from '../../app/App';
import { HP, WP, size } from '../../services';
import Icon, { IconTypes } from '../Icon/Icon';
import { PostButton } from '../Buttons/PostButton';
import PaginatedList from '../PaginatedList';
import { PremiumIdeaFeed as PremiumFeed } from '@benzinga/trade-ideas-manager';
import LoadingView from '../LoadingView';
import { ExpertsArrayProps } from '../../screens/Ideas/PremiumCardScreen';
import { useDispatch } from 'react-redux';
import { loadAllPremiumIdeas, loadMorePremiumIdeas, loadPremiumIdeas } from '../../redux/actions';
import { useAppSelector } from '../../redux/hooks';
import AlertModal from 'react-native-modal';
import CustomPressable from '../CustomPressable';
import Data from '../../data-mobile/data';
import { filterOptions } from '../../constants/Constants';

interface PremiumIdeaFeedProps {
  expertsArray: ExpertsArrayProps[];
  ideasFeed: PremiumFeed;
  feedType: string;
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
}

interface RenderNoExpertSubscriptionComponentProps {
  idea?: any;
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
}

const PremiumIdeaFeed = ({ expertsArray, feedType, ideasFeed, navigation }: PremiumIdeaFeedProps) => {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const type = feedType === 'stock' ? 'premiumStocksTradeIdeas' : 'premiumOptionsTradeIdeas';
  const [selectedExpert, setSelectedExpert] = useState<ExpertsArrayProps>(expertsArray[0]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [premiumIdeas, setPremiumIdeas] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [filterTradeIndex, setFilterTradeIndex] = useState(-1);
  const dispatch = useDispatch();
  const storedIdeas = useAppSelector(state => state.idea);

  useEffect(() => {
    if (Platform.OS === 'android') {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }, []);

  useEffect(() => {
    if (filterTradeIndex !== -1) {
      loadIdeas(true, 0);
    }
  }, [filterTradeIndex]);

  useEffect(() => {
    const productId = selectedExpert.product_id;
    if (storedIdeas[type][productId]?.premiumTrades?.length) {
      setPremiumIdeas(storedIdeas[type][productId]?.premiumTrades);
    } else {
      loadIdeas(true);
    }
  }, [selectedExpert]);

  const loadIdeas = useCallback(
    async (willLoad = false, pageNo = 0) => {
      willLoad ? setIsLoading(true) : setHasMore(true);
      let mavenTradesFeed = ideasFeed;
      if (filterTradeIndex !== -1 && filterTradeIndex !== 4) {
        mavenTradesFeed =
          feedType === 'stock'
            ? Data.tradeIdeas().premiumFeed(
                { limit: 10, status: filterOptions[filterTradeIndex].status },
                'stock | ticker',
              )
            : Data.tradeIdeas().premiumFeed({ limit: 10, status: filterOptions[filterTradeIndex].status }, 'option');
      }
      const cbSuccess = (ideas: { ok: any }) => {
        setPremiumIdeas(ideas.ok);
        willLoad ? setIsLoading(false) : setHasMore(false);
      };
      if (selectedExpert.instructorName === 'All') {
        if (pageNo === 0) {
          dispatch(
            loadAllPremiumIdeas(
              {
                ideasFeed: mavenTradesFeed,
                expertProductId: selectedExpert.product_id,
                reload: true,
                filter: feedType,
              },
              cbSuccess,
            ),
          );
        } else {
          dispatch(
            loadAllPremiumIdeas(
              {
                ideasFeed: mavenTradesFeed,
                expertProductId: selectedExpert.product_id,
                reload: false,
                filter: feedType,
              },
              cbSuccess,
            ),
          );
        }
      } else if (pageNo === 0) {
        dispatch(
          loadPremiumIdeas(
            { ideasFeed: mavenTradesFeed, expertProductId: selectedExpert.product_id, filter: feedType },
            cbSuccess,
          ),
        );
      } else {
        dispatch(
          loadMorePremiumIdeas(
            { ideasFeed: mavenTradesFeed, expertProductId: selectedExpert.product_id, filter: feedType },
            cbSuccess,
          ),
        );
      }
    },
    [selectedExpert, filterTradeIndex],
  );

  const renderExpertOptions = () => {
    return (
      <View style={styles.expertOptionsContainer}>
        <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
          {expertsArray.map((expert, index) => {
            return (
              <Pressable
                style={({ pressed }) => [
                  {
                    backgroundColor: expert === selectedExpert ? colors.buttonBlueDefault : colors.newCard,
                    opacity: pressed ? 0.5 : 1,
                  },
                  styles.expertOptionsPressableStyle,
                ]}
                onPress={() => {
                  setSelectedExpert(expert);
                }}
                key={index}
              >
                <Text key={index} style={styles.expertOptionsText}>
                  {expert.instructorName.toUpperCase()}
                </Text>
              </Pressable>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  const renderExpertsDropDown = () => {
    return (
      <TouchableOpacity
        style={[
          styles.expertsDropDownContainer,
          {
            backgroundColor: isExpanded ? colors.grayishBlue : colors.newCard,
            marginBottom: 10,
          },
        ]}
        onPress={() => {
          setIsExpanded(!isExpanded);
        }}
      >
        <Image
          source={{
            uri: selectedExpert.profileUrl
              ? selectedExpert.profileUrl
              : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSj06ORgLc09irkPuj3XplptRIDCv52UUahxCGmCS8Gow&s',
          }}
          style={styles.profileImageStyle}
        />
        <Text style={[styles.boldText]}>{selectedExpert.instructorName.toUpperCase()}</Text>
        <View style={styles.chevronUpDownContainer}>
          <Icon
            type={IconTypes.EvilIcons}
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={30}
            color={colors.text}
          />
        </View>
      </TouchableOpacity>
    );
  };

  const renderExpertsDropDownList = () => {
    return (
      <ScrollView
        contentContainerStyle={{
          marginHorizontal: 15,
        }}
        style={{ marginVertical: 8 }}
      >
        {expertsArray.map((expert, index) => {
          return (
            <Pressable
              onPress={() => {
                setSelectedExpert(expert);
                setIsExpanded(!isExpanded);
              }}
              style={styles.expertsDropDownListContainer}
              key={index}
            >
              <Image
                source={{
                  uri: expert.profileUrl
                    ? expert.profileUrl
                    : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSj06ORgLc09irkPuj3XplptRIDCv52UUahxCGmCS8Gow&s',
                }}
                style={styles.profileImageStyle}
              />
              <Text style={[styles.boldText, { marginLeft: 10 }]}>{expert.instructorName}</Text>
            </Pressable>
          );
        })}
      </ScrollView>
    );
  };

  const renderExpertsList = () => {
    return (
      <AlertModal
        isVisible={isExpanded}
        hasBackdrop={true}
        style={styles.modalContainer}
        animationIn="slideInUp"
        animationInTiming={300}
        animationOut="slideOutDown"
        animationOutTiming={300}
        onBackdropPress={() => {
          setIsExpanded(!isExpanded);
        }}
        onBackButtonPress={() => {
          setIsExpanded(!isExpanded);
        }}
      >
        <Text style={styles.expertsListText}>Expert's List</Text>
        {renderExpertsDropDownList()}
      </AlertModal>
    );
  };

  const renderEmptyView = () => {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: colors.cardBackground }]}>
        <Text style={[styles.emptyText, { color: colors.textInverted, textAlign: 'center' }]}>No Rows To Show</Text>
      </View>
    );
  };

  const handleFilterModal = (value: boolean) => {
    setIsFilterModalVisible(value);
  };

  return (
    <View style={styles.mainContainer}>
      {selectedExpert.instructorName === 'All' ? renderExpertOptions() : renderExpertsDropDown()}
      {renderExpertsList()}
      <CustomPressable onPress={() => handleFilterModal(true)} style={styles.filterIcon}>
        {filterTradeIndex === -1 ? (
          <Icon type={IconTypes.MaterialCommunityIcons} name={'filter'} size={35} color={'white'} />
        ) : (
          <Icon type={IconTypes.MaterialCommunityIcons} name={'filter-check'} size={35} color={'white'} />
        )}
      </CustomPressable>
      <AlertModal
        isVisible={isFilterModalVisible}
        hasBackdrop={true}
        style={{ zIndex: 1 }}
        animationIn="slideInUp"
        animationInTiming={300}
        animationOut="slideOutDown"
        animationOutTiming={300}
        onBackdropPress={() => {
          setIsFilterModalVisible(!isFilterModalVisible);
        }}
        onBackButtonPress={() => {
          setIsFilterModalVisible(!isFilterModalVisible);
        }}
      >
        <View style={styles.filterModalContainer}>
          <Text style={styles.filterModalTitleText}>Filter Your Trades</Text>
          {filterOptions.map((item, index) => {
            return (
              <CustomPressable
                style={{ flexDirection: 'row', marginVertical: 4, marginLeft: 8, alignItems: 'center' }}
                key={`filter-${index}`}
                onPress={() => {
                  setFilterTradeIndex(index);
                  setIsFilterModalVisible(false);
                }}
              >
                {filterTradeIndex === index ? (
                  <Icon type={IconTypes.Octicons} name={'check-circle'} size={16} color={colors.linkText} />
                ) : (
                  <Icon type={IconTypes.Octicons} name={'circle'} size={16} color={colors.text} />
                )}
                <Text
                  style={{
                    color: filterTradeIndex === index ? colors.linkText : colors.text,
                    fontSize: 14,
                    marginLeft: 8,
                  }}
                >
                  {item.type}
                </Text>
              </CustomPressable>
            );
          })}
        </View>
      </AlertModal>
      {isLoading ? (
        <LoadingView style={{ justifyContent: 'center', flex: 1 }} />
      ) : selectedExpert.locked ? (
        <FlatList
          data={premiumIdeas}
          ListEmptyComponent={renderEmptyView}
          renderItem={({ index, item }) => {
            return (
              <View style={{ marginVertical: 5 }} key={`empty-view-${index}`}>
                <RenderNoExpertSubscriptionComponent
                  idea={item}
                  navigation={navigation}
                  key={`render-no-expert-sub-component-${index}`}
                />
              </View>
            );
          }}
          style={{ paddingBottom: WP(10), backgroundColor: colors.cardBackground }}
          contentContainerStyle={{ backgroundColor: colors.cardBackground, flexGrow: 1 }}
        />
      ) : (
        <PaginatedList
          style={{ paddingBottom: WP(10), backgroundColor: colors.cardBackground }}
          containerStyle={{ backgroundColor: colors.cardBackground }}
          pageSize={10}
          loadData={(pageNo: number) => {
            if (pageNo === 0) {
              loadIdeas(true);
            } else {
              loadIdeas(false, pageNo);
            }
          }}
          hasMore={hasMore}
          listEmptyComponent={renderEmptyView}
          data={premiumIdeas}
          renderItem={({ index, item }: { index: number; item: any }) => {
            return (
              <View style={{ marginVertical: 5 }} key={index}>
                {selectedExpert.instructorName === 'All' ? (
                  expertsArray.map(expert => {
                    if (expert.product_id === item.package.product_id) {
                      return expert.locked ? (
                        <RenderNoExpertSubscriptionComponent idea={item} navigation={navigation} key={index} />
                      ) : (
                        <PremiumCard navigation={navigation} idea={item} key={index} />
                      );
                    }
                    return null;
                  })
                ) : (
                  <PremiumCard navigation={navigation} idea={item} key={index} />
                )}
              </View>
            );
          }}
        />
      )}
    </View>
  );
};

const RenderNoExpertSubscriptionComponent = ({ idea, navigation }: RenderNoExpertSubscriptionComponentProps) => {
  const { colors, isDark } = useTheme();
  const styles = withColors(colors);

  const [cardExpanded, setCardExpanded] = useState(false);

  return (
    <Pressable
      onPress={() => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setCardExpanded(!cardExpanded);
      }}
      style={styles.container}
    >
      <PremiumCardHeader idea={idea} cardExpanded={cardExpanded} navigation={navigation} />
      <Image
        source={
          isDark ? require('../../assets/images/card_blur.png') : require('../../assets/images/card_blur_light.png')
        }
        style={styles.blurImageStyle}
        resizeMode="stretch"
      />
      {cardExpanded && (
        <>
          <View style={{ alignItems: 'center', marginTop: -10 }}>
            <Icon type={IconTypes.EvilIcons} name="lock" size={72} color={'#727A86'} />
          </View>
          <Text style={[styles.noExpertPremiumTextStyle, { marginTop: 20 }]}>You don't have access to this idea.</Text>
          <Text
            style={[
              styles.noExpertPremiumTextStyle,
              {
                marginHorizontal: 78,
              },
            ]}
          >
            Please upgrade your Premium
          </Text>
          <Text
            style={[
              styles.noExpertPremiumTextStyle,
              {
                marginHorizontal: 78,
                marginBottom: 31,
              },
            ]}
          >
            subscription for access.
          </Text>
          <PostButton
            containerStyle={styles.getAccessBtnContainerStyle}
            style={styles.getAccessBtnStyle}
            title="Get Access"
            onPress={() => {
              navigation.navigate('MySubscription');
            }}
          />
        </>
      )}
    </Pressable>
  );
};

export default PremiumIdeaFeed;

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    container: {
      backgroundColor: colors.newCard,
      borderRadius: 5,
      paddingHorizontal: WP('3'),
      paddingTop: 8,
      marginHorizontal: 15,
    },
    mainContainer: {
      flex: 1,
      backgroundColor: colors.cardBackground,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.cardBackground,
    },
    emptyText: {
      color: colors.textInverted,
      fontSize: 20,
    },
    boldText: {
      color: colors.text,
      fontWeight: 'bold',
      fontSize: size.small,
      alignSelf: 'center',
      marginLeft: 8,
    },
    dividerComponent: {
      borderWidth: 0.4,
      borderTopColor: colors.border,
      borderBottomColor: colors.newCard,
      marginTop: 16,
      marginHorizontal: 15,
    },
    profileImageStyle: {
      height: 24,
      width: 24,
      borderRadius: 12,
      margin: 4,
    },
    expertsDropDownContainer: {
      marginHorizontal: 15,
      marginTop: 10,
      height: 32,
      borderRadius: 20,
      flexDirection: 'row',
    },
    expertsDropDownListContainer: {
      backgroundColor: colors.newCard,
      borderRadius: 20,
      flexDirection: 'row',
      height: 32,
      marginVertical: 2,
    },
    expertOptionsContainer: {
      marginHorizontal: 15,
      marginVertical: 10,
    },
    expertOptionsPressableStyle: {
      height: 32,
      paddingHorizontal: 12,
      justifyContent: 'center',
      marginRight: 5,
      alignSelf: 'center',
      borderRadius: 20,
    },
    expertOptionsText: {
      color: colors.textInverted,
      fontSize: 14,
      textAlign: 'center',
    },
    chevronUpDownContainer: {
      flexDirection: 'row-reverse',
      alignSelf: 'center',
      flex: 1,
      marginHorizontal: 20,
    },
    blurImageStyle: {
      width: '104%',
      justifyContent: 'center',
      alignItems: 'center',
      height: 60,
      marginBottom: 4,
      marginLeft: -8,
    },
    noExpertPremiumTextStyle: {
      fontSize: 14,
      fontWeight: '700',
      color: colors.text,
      textAlign: 'center',
    },
    getAccessBtnContainerStyle: {
      paddingLeft: 0,
      marginHorizontal: -WP(3),
    },
    getAccessBtnStyle: {
      backgroundColor: colors.grayishBlue,
      borderWidth: 0,
      height: 40,
      marginBottom: 0,
      marginHorizontal: 0,
      paddingHorizontal: -WP(3),
    },
    modalContainer: {
      position: 'absolute',
      width: '90%',
      height: '50%',
      backgroundColor: colors.cardBackground,
      bottom: HP(25),
      margin: 0,
      alignSelf: 'center',
      borderRadius: 8,
    },
    expertsListText: {
      textAlign: 'center',
      marginTop: 8,
      fontWeight: 'bold',
      fontSize: size.large,
      color: colors.text,
    },
    filterIcon: {
      zIndex: 1,
      position: 'absolute',
      right: 25,
      bottom: HP(4),
      height: 50,
      width: 50,
      borderRadius: 25,
      backgroundColor: colors.textBlue,
      alignItems: 'center',
      justifyContent: 'center',
    },
    filterModalContainer: {
      backgroundColor: colors.cardBackground,
      padding: 8,
      borderRadius: 8,
      width: '70%',
      alignSelf: 'center',
    },
    filterModalTitleText: {
      color: colors.text,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 4,
    },
  });
};
