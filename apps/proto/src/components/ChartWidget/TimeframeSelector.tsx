import React from 'react';
import { ChartRange } from '@benzinga/chart-manager';

interface TimeframeSelectorProps {
  currentTimeframe: ChartRange;
  onTimeframeChange?: (timeframe: ChartRange) => void;
}

export const VALID_TIMEFRAMES: ChartRange[] = ['1d', '5d', '1m', '3m', '6m', 'YTD', '1y', '2y', '5y'];

const TimeframeSelector: React.FC<TimeframeSelectorProps> = ({ currentTimeframe, onTimeframeChange }) => {
  const timeframeGroups = {
    Day: ['1d', '5d'],
    Month: ['1m', '3m', '6m'],
    Year: ['YTD', '1y', '2y', '5y'],
  };

  const [isOpen, setIsOpen] = React.useState(false);

  const renderMobileSelector = () => (
    <div className="relative sm:hidden w-full">
      <button
        className="w-full px-4 py-2 text-left bg-white border rounded-lg shadow flex justify-between items-center"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{currentTimeframe as string}</span>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path d="M19 9l-7 7-7-7" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg">
          {Object.entries(timeframeGroups).map(([group, tfs]) => (
            <div key={group}>
              <div className="px-4 py-2 text-sm font-semibold text-gray-500 bg-gray-50">{group}</div>
              {tfs.map(tf => (
                <button
                  className={`w-full px-4 py-2 text-left text-sm ${
                    currentTimeframe === tf ? 'bg-blue-50 text-blue-600' : 'hover:bg-gray-50'
                  }`}
                  key={tf}
                  onClick={() => {
                    onTimeframeChange?.(tf as ChartRange);
                    setIsOpen(false);
                  }}
                >
                  {tf}
                </button>
              ))}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderDesktopSelector = () => (
    <div className="hidden sm:flex flex-wrap gap-1 md:gap-2">
      {VALID_TIMEFRAMES.map(tf => (
        <button
          className={`px-2 py-1 text-xs md:text-sm rounded transition-colors ${
            currentTimeframe === tf ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
          key={tf as string}
          onClick={() => onTimeframeChange?.(tf)}
        >
          {tf as string}
        </button>
      ))}
    </div>
  );

  return (
    <>
      {renderMobileSelector()}
      {renderDesktopSelector()}
    </>
  );
};

export default TimeframeSelector;
