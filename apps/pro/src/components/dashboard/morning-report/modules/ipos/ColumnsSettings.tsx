import TickerColumn from '../../components/table/columns/TickerColumn';
import DateColumn from '../../components/table/columns/DateColumn';
import RangeColumn from '../../components/table/columns/RangeColumn';
import TextColumn from '../../components/table/columns/TextColumn';

export const columns = [
  {
    dataIndex: 'ticker',
    key: 'ticker',
    render: (value: string, record) => <TickerColumn description={record.name} layout={'compact'} ticker={value} />,
    title: 'Ticker',
    width: '110px',
  },
  {
    dataIndex: 'date',
    key: 'date',
    render: (value: string, record) => <DateColumn date={value} display="date" time={record.time} />,
    title: 'Date',
    width: '110px',
  },
  {
    dataIndex: 'exchange',
    key: 'exchange',
    render: (value: string) => <TextColumn variant="small-bold">{value}</TextColumn>,
    title: 'Exchange',
    width: '120px',
  },
  {
    dataIndex: 'pricing_date',
    key: 'ipoDate',
    render: (value: string) => <DateColumn date={value} display="date" />,
    title: 'Date of IPO',
    width: '120px',
  },
  {
    dataIndex: 'price_min',
    key: 'price',
    render: (value: number, record) => (
      <RangeColumn from={Number(value ?? 0).toFixed(2)} to={Number(record.price_max ?? 0).toFixed(2)} />
    ),
    title: () => (
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>Price Min.</span> - <span>Price Max.</span>
      </div>
    ),
    width: '180px',
  },
];
