import { Typography } from 'antd';
import PriceColumn from '../../components/table/columns/PriceColumn';
import TextColumn from '../../components/table/columns/TextColumn';
import DateColumn from '../../components/table/columns/DateColumn';
import { FlagIcon } from '@benzinga/pro-ui';

const { Text } = Typography;

export const columns = [
  {
    dataIndex: 'event_name',
    key: 'event_name',
    render: (value: string, record) => (
      <>
        {record?.country && <FlagIcon code={record.country} />}
        <TextColumn tooltip variant="big">
          {value}
        </TextColumn>
      </>
    ),
    title: 'Economic Event',
    width: 140,
  },
  {
    dataIndex: 'time',
    key: 'time',
    render: (value: string, record) => {
      return <DateColumn date={record.date} display="time" time={value} />;
    },
    title: 'Time',
    width: 40,
  },
  {
    dataIndex: 'consensus',
    key: 'estimates',
    render: (value: string, record) => {
      if (!value) {
        return '--';
      }

      return (
        <Text
          style={{
            color: Number(value) >= 0 ? 'var(--bz-positive)' : 'var(--bz-negative)',
            fontSize: '16px',
            fontWeight: 700,
          }}
        >
          <PriceColumn record={record} value={value} />
        </Text>
      );
    },
    title: 'Estimates',
    width: 70,
  },
];
