import React, { useEffect, useMemo } from 'react';
import ModuleWrapper from '../../components/module/ModuleWrapper';
import { SessionContext } from '@benzinga/session-context';
import { columns } from './ColumnsSettings';
import { ModuleTable } from '../../components/table/ModuleTable';
import { CalendarManager, Earnings as BaseEarnings } from '@benzinga/calendar-manager';

import { DateTime } from 'luxon';
import { getMarketTimeStatus, MarketTimeStatus } from '../../components/table/utils/time';
import AddToWatchlist from '../../components/watchlist/AddToWatchlist';
import { QuotesManager } from '@benzinga/quotes-manager';

interface Earnings extends BaseEarnings {
  marketCap: number;
}

const BUCKET_MAX_SIZE = 10;

const TopEarningsModule: React.FC = () => {
  const session = React.useContext(SessionContext);
  const [loading, setLoading] = React.useState(true);
  const [data, setData] = React.useState<Earnings[]>([]);

  const tickers = useMemo(() => {
    return data?.map(earning => earning.ticker) ?? [];
  }, [data]);

  useEffect(() => {
    const fetchEarningsData = async () => {
      try {
        const calendarManager = session.getManager(CalendarManager);
        const response = await calendarManager.getCalendarData(
          'earnings',
          { date: DateTime.now().toFormat('yyyy-LL-dd') },
          true,
        );

        const earningsData = response?.ok ?? [];

        const preMarket: Earnings[] = [];
        const afterMarket: Earnings[] = [];

        const quotesManager = session.getManager(QuotesManager);
        const quoteResponse = await quotesManager.getDelayedQuotes(earningsData.map(earning => earning.ticker));
        const quotes = quoteResponse?.ok ?? {};

        earningsData.forEach(earning => {
          const marketCap = quotes[earning.ticker]?.marketCap ?? 0;
          const marketTimeStatus = getMarketTimeStatus(earning.time);

          if (marketTimeStatus === MarketTimeStatus.BEFORE_MARKET) {
            preMarket.push({ ...earning, marketCap });
          } else if (marketTimeStatus === MarketTimeStatus.AFTER_MARKET) {
            afterMarket.push({ ...earning, marketCap });
          }
        });

        const sortTopEarnings = (earnings: Earnings[]) =>
          earnings
            .sort((a, b) => b.marketCap - a.marketCap)
            .slice(0, BUCKET_MAX_SIZE)
            .sort((a, b) => DateTime.fromISO(a.time).toMillis() - DateTime.fromISO(b.time).toMillis());

        setData([...sortTopEarnings(preMarket), ...sortTopEarnings(afterMarket)]);
      } catch (error) {
        console.error('Error fetching earnings data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEarningsData();
  }, [session]);

  return (
    <ModuleWrapper
      title="Top Earnings For The Day"
      titleAffix={!!tickers?.length && <AddToWatchlist symbols={tickers} />}
    >
      <ModuleTable columns={columns} dataSource={data} loading={loading} pagination={false} tableLayout="auto" />
    </ModuleWrapper>
  );
};

export default TopEarningsModule;
