'use client';
import React, { useState, useEffect, startTransition, Suspense } from 'react';

import { usePermission } from '@benzinga/user-context';
import { BullBearStatements } from '@benzinga/quotes-manager';
import { QuoteProfile } from '../../../../entities/quoteEntity';
import { BearSays, BullSays } from '@benzinga/icons';
import { Button } from '@benzinga/core-ui';
import styles from './styles.module.scss';
import { useRouter } from 'next/navigation';

const BzEdgeCTA = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.BzEdgeCTA })));

type BullsVsBearsProps = {
  profile?: QuoteProfile;
  statements: BullBearStatements | null;
  symbol?: string;
};

const GetAccessBlock: React.FC = () => {
  const [showTextVersion, setShowTextVersion] = useState(false);

  useEffect(() => {
    const versionIndex = Math.floor(Math.random() * 2);
    startTransition(() => {
      setShowTextVersion(versionIndex === 1);
    });
  }, []);

  if (showTextVersion) {
    return (
      <div className={styles.getAccessWrapper}>
        <div className={styles.accessHeader}>
          <div className={styles.icons}>
            <div className={styles.iconWrapper}>
              <BearSays />
            </div>
            <div className={styles.iconWrapper}>
              <BullSays />
            </div>
          </div>
          <h3 className={styles.accessTitle}>
            Unlock major analysts&apos; bullish and bearish positions by joining Benzinga Edge.
          </h3>
        </div>
        <a
          className="w-full sm:w-auto text-center"
          href="https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?t=be8be9we4gewe1be11&utm_source=bulls-vs-bears"
          target="_blank"
        >
          Click Here
        </a>
      </div>
    );
  }

  return (
    <Suspense fallback={<div />}>
      <BzEdgeCTA onlyGraphic={true} type="bulls-vs-bears" />
    </Suspense>
  );
};

export const FullDisplayBullVsBear: React.FC<BullsVsBearsProps> = ({ profile, statements }) => {
  const [show, setShow] = useState(false);
  const name = profile?.richQuoteData?.name;
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const noDataString = 'No data available';

  useEffect(() => {
    if (hasPermission) {
      setShow(true);
    }
  }, [hasPermission]);

  if (!statements) {
    return null;
  }

  if (!show) {
    return <GetAccessBlock />;
  }

  return (
    <div className={styles.fullDisplayWrapper}>
      <div>
        <h3>
          <div>
            <BullSays />
          </div>
          What Bulls are Saying about {name}
        </h3>
        <p className="h-full">{statements?.bull_case || noDataString}</p>
      </div>
      <div>
        <h3>
          <div>
            <BearSays />
          </div>
          What Bears are Saying about {name}
        </h3>
        <p className="h-full">{statements?.bear_case || noDataString}</p>
      </div>
    </div>
  );
};

export const BullsVsBears: React.FC<BullsVsBearsProps> = ({ statements, symbol }) => {
  const [show, setShow] = useState(false);
  const [showMobileReadMore, setShowMobileReadMore] = useState(false);
  const [showBear, setShowBear] = useState(true);

  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const router = useRouter();

  const noDataString = 'No data available';

  useEffect(() => {
    if (hasPermission) {
      setShow(true);
    }
  }, [hasPermission]);

  if (!statements) {
    return null;
  }

  if (!show) {
    return <GetAccessBlock />;
  }

  const goToAnalystRatings = () => {
    router.push(`/quote/${symbol}/analyst-ratings`);
  };

  return (
    <div className={styles.rowWrapper}>
      <div className={styles.mainContent}>
        <div className={styles.card} onClick={goToAnalystRatings}>
          <div className={styles.title}>
            <h3>Bear says</h3>
            <BearSays />
          </div>
          <p className={styles.preview}>{statements?.bear_case || noDataString}</p>
          <div className={`${styles.descriptionTooltip} ${styles.bear}`}>{statements?.bear_case || noDataString}</div>
        </div>
        <div className={styles.divider}></div>
        <div className={styles.card} onClick={goToAnalystRatings}>
          <div className={styles.title}>
            <h3>Bull says</h3>
            <BullSays />
          </div>
          <p className={styles.preview}>{statements?.bull_case || noDataString}</p>
          <div className={`${styles.descriptionTooltip} ${styles.bull}`}>{statements?.bull_case || noDataString}</div>
        </div>
      </div>
      <div className={styles.mobileContent}>
        <Button
          className="uppercase mt-2"
          onClick={() => setShowMobileReadMore(!showMobileReadMore)}
          variant="flat-light-blue"
        >
          {showMobileReadMore ? 'Close' : 'Read More'}
        </Button>
        {showMobileReadMore && (
          <div className={styles.descriptionCard}>
            <div className={styles.cardHeader}>
              <h3>
                {showBear ? <BearSays /> : <BullSays />}
                {showBear ? 'Bear Says' : 'Bull Says'}
              </h3>
              <button onClick={() => setShowBear(!showBear)}>Switch to {showBear ? 'Bull' : 'Bear'} Says</button>
            </div>
            <p>{statements?.[showBear ? 'bear_case' : 'bull_case'] || 'No data available'}</p>
          </div>
        )}
      </div>
    </div>
  );
};
