import React from 'react';
import classNames from 'classnames';
import styled from '@benzinga/themetron';
import { Rate } from '@benzinga/core-ui';
import { numberShorthand } from '@benzinga/utils';
import { QuoteProfile } from '../../../entities/quoteEntity';
import InfoTooltip from './InfoTooltip';
import { EdgeRanking } from '@benzinga/ticker-ui';
import { RankingDetail } from '@benzinga/quotes-manager';
import { Tooltip, Icon } from '@benzinga/core-ui';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';

interface ETFOverviewHeaderProps {
  profile: QuoteProfile;
  rankingData?: RankingDetail | null;
}

export const ETFOverviewHeader = ({ profile, rankingData }: ETFOverviewHeaderProps) => {
  return (
    <div className="flex gap-2  flex-col lg:flex-row">
      {rankingData && (
        <div className="flex flex-1 flex-col">
          <div className="flex flex-row gap-2 pb-2">
            <h6 className="uppercase pb-0">Edge Rankings</h6>
            <Tooltip
              content={
                'Benzinga Edge stock rankings give you four critical scores to help you identify the strongest and weakest stocks to buy and sell.'
              }
              width={250}
            >
              <Icon
                className="info-circle-icon text-sm flex items-center justify-center text-[#99AECC]"
                icon={faInfoCircle}
              />
            </Tooltip>
          </div>
          <EdgeRanking
            className="w-full"
            {...rankingData}
            adType="quotepage"
            layout="crypto_etf"
            symbol={profile?.symbol}
            variant="dark"
          />
        </div>
      )}
      <OverviewRowWrapper className="flex justify-between rounded border border-bzblue-400 p-4 flex-1">
        <div className="flex flex-col md:flex-row items-center justify-between w-full font-bold items-[normal]">
          <div className="flex flex-col md:flex-row flex-wrap gap-x-4 min-h-16 w-full">
            <div className="flex flex-col justify-center">
              <div className="text-gray-600 text-base uppercase">Sector</div>
              <div className="text-blue-600 text-xl truncate overflow-hidden max-w-[350px]">
                {profile?.etfFund?.sector || '-'}
              </div>
            </div>
            <Divider />
            <div className="flex gap-4">
              <div className="flex flex-col justify-center flex-1">
                <div className="text-gray-600 text-base uppercase">Region</div>
                <div className="text-blue-600 text-xl">{profile.etfFund?.region || '-'}</div>
              </div>
              <Divider variant="vertical" />
              <div className="flex flex-col justify-center flex-1">
                <div className="text-gray-600 text-base uppercase">AUM</div>
                <div className="text-blue-600 text-xl">
                  {profile?.etfFund?.fund_aum ? numberShorthand(profile?.etfFund?.fund_aum, 2) : '-'}
                </div>
              </div>
            </div>
          </div>
          <div className="w-full md:hidden">
            <Divider />
          </div>
          <div className="flex flex-col items-center md:ml-auto mt-2">
            {/* <div className="text-gray-600 text-base uppercase mt-2">Fund Score</div> */}
            <div className="flex flex-col items-center w-full">
              <Rate
                allowHalf={true}
                className="mb-2"
                readOnly={true}
                size={28}
                value={profile.etfFund?.qm_fund_score || 0}
              />
              <InfoTooltip
                title="Fund Score"
                tooltipContent="QuoteMedia's proprietary rating methodology that takes into account a number of factors including qualitative assessments about the fund management, portfolio, and markets"
              />
            </div>
          </div>
        </div>
      </OverviewRowWrapper>
    </div>
  );
};

export default ETFOverviewHeader;

const Divider: React.FC<{ variant?: 'horizontal' | 'vertical' }> = ({ variant }) => {
  return (
    <DividerWrapper className={classNames('divider-wrapper', variant)}>
      <div className="divider bg-blue-100" />
    </DividerWrapper>
  );
};

const DividerWrapper = styled.div`
  padding: 2px 16px;

  @media (max-width: 768px) {
    padding: 16px 0;
  }

  .divider {
    width: 2px;
    height: 100%;

    @media (max-width: 768px) {
      width: 100%;
      height: 2px;
    }
  }

  &.horizontal {
    .divider {
      width: 100%;
      height: 2px;
    }
  }
  &.vertical {
    padding: 2px 16px;
    .divider {
      width: 2px;
      height: 100%;
    }
  }
`;

const OverviewRowWrapper = styled.div`
  background: #f2f8ff;

  .overview-title {
    color: #5b7292;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 700;
  }
`;
