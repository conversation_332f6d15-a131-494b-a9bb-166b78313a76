import { SectionTitle } from '@benzinga/core-ui';
import Peers from './Peers';
import type { DelayedQuote, QuoteSessionType } from '@benzinga/quotes-manager';
import type { ChartDataResponse } from '@benzinga/chart-manager';
import { useTranslation } from 'react-i18next';
import i18n from '@benzinga/translate';

interface DrilldownFooterProps {
  advertiserProfile?: any;
  isAdvertiser: boolean;
  isETF: boolean;
  peersChartData: Record<string, ChartDataResponse> | null;
  symbol: string;
  leftSection: React.ReactNode | null;
  type: QuoteSessionType;
  peers: Record<string, DelayedQuote>;
}

const DrilldownFooter: React.FC<DrilldownFooterProps> = ({
  isAdvertiser,
  isETF,
  leftSection,
  peers,
  peersChartData,
  symbol,
  type,
}) => {
  const { t } = useTranslation('quote', { i18n });

  return (
    <div className="">
      <div className="flex flex-col gap-8 md:gap-4 lg:flex-row">
        <div className={`w-full ${!isAdvertiser ? 'lg:w-9/12' : ''}`}>{leftSection}</div>
        {!isETF && !isAdvertiser && peersChartData && Object.keys(peersChartData).length > 0 && (
          <div className="w-full lg:w-3/12">
            <SectionTitle level={3} size="2xl">
              {t('Quote.Peers.people-also-watch')}
            </SectionTitle>
            {peersChartData && <Peers chartData={peersChartData} peers={peers} symbol={symbol} type={type} />}
          </div>
        )}
      </div>
    </div>
  );
};

export default DrilldownFooter;
