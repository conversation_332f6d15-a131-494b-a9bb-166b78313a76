'use client';

import { createG<PERSON>balStyle, StyleSheetManager, ThemeProvider } from '@benzinga/themetron';
import React from 'react';
import { CookiesProvider } from 'react-cookie';
import { Toaster } from 'react-hot-toast';
import { SessionContextProvider } from '@benzinga/session-context';
import { ErrorBoundary } from '@benzinga/core-ui';
import { DEFAULT_LANGUAGE } from '@benzinga/translate';

import { getGlobalSession } from '../../../pages/api/session';
import { PagePropsType } from '../../../utils/getServerProps';
import { RootSchema } from './RootSchema';

import '@benzinga/globalStyles';
import './wrapProvider.scss';
import '../../../utils/font-awesome';

const session = getGlobalSession();

function WrapProviders({
  children,
  pageProps,
}: {
  children: React.ReactNode;
  isLoggedIn: boolean;
  pageProps?: PagePropsType;
}): JSX.Element | null {
  // useEffect(() => {
  //   if ('Notification' in window && Notification.permission === 'granted') {
  //     const host = window.location.host;
  //     session?.getManager(NotificationManager).setupServiceWorker({ global_key: host });
  //   }
  // }, []);
  return (
    <>
      <GlobalTheme locale={pageProps?.metaProps?.language ?? DEFAULT_LANGUAGE} />
      <SessionContextProvider session={session}>
        <ErrorBoundary disableFallback={true} name="global">
          <CookiesProvider>
            <StyleSheetManager>
              <ThemeProvider theme="modern">
                <RootSchema metaInfo={pageProps?.metaProps || {}} />
                {children}
                <div id="app-modal-element"></div>
                <div id="__next"></div>
                <Toaster position="top-center" />
              </ThemeProvider>
            </StyleSheetManager>
          </CookiesProvider>
        </ErrorBoundary>
      </SessionContextProvider>
    </>
  );
}

export default WrapProviders;
const GlobalTheme = createGlobalStyle<{ locale: string }>`
  ${props =>
    ['ko', 'ja'].includes(props.locale)
      ? `*, h1, h2, h3, h4, h5 {
    font-family: Noto Sans KR, Manrope, Manrope-fallback, sans-serif;
  }`
      : ''}`;
